
#include "btn_app.h"
#include "ebtn.h" 
#include "gpio.h" 

extern uint8_t ucLed[6]; 

typedef enum
{
    USER_BUTTON_0 = 0, 
    USER_BUTTON_1,     
    USER_BUTTON_2,     
    USER_BUTTON_3,     
    USER_BUTTON_4,    
    USER_BUTTON_5,     
    USER_BUTTON_MAX,   
  
} user_button_t;


static const ebtn_btn_param_t defaul_ebtn_param = EBTN_PARAMS_INIT(20, 0, 20, 1000, 0, 1000, 10);

static ebtn_btn_t btns[] = { 
    EBTN_BUTTON_INIT(USER_BUTTON_0, &defaul_ebtn_param), // ????0
    EBTN_BUTTON_INIT(USER_BUTTON_1, &defaul_ebtn_param), // ????1
    EBTN_BUTTON_INIT(USER_BUTTON_2, &defaul_ebtn_param), // ????2
    EBTN_BUTTON_INIT(USER_BUTTON_3, &defaul_ebtn_param), // ????3
    EBTN_BUTTON_INIT(USER_BUTTON_4, &defaul_ebtn_param), // ????4
    EBTN_BUTTON_INIT(USER_BUTTON_5, &defaul_ebtn_param), // ????5
};

// static ebtn_btn_combo_t btns_combo[] = {
//     EBTN_BUTTON_COMBO_INIT_RAW(USER_BUTTON_COMBO_0, &defaul_ebtn_param, EBTN_EVT_MASK_ONCLICK),
//     EBTN_BUTTON_COMBO_INIT_RAW(USER_BUTTON_COMBO_1, &defaul_ebtn_param, EBTN_EVT_MASK_ONCLICK),
// };

uint8_t prv_btn_get_state(struct ebtn_btn *btn) // ????????? ????:??????????? ????:??????(1=????,0=???)
{
    switch (btn->key_id) // ???????ID???GPIO??
    {
    case USER_BUTTON_0:
        return !HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_15); // ????0(PE15)????????
    case USER_BUTTON_1:
        return !HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_13); // ????1(PE13)????????
    case USER_BUTTON_2:
        return !HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_11); // ????2(PE11)????????
    case USER_BUTTON_3:
        return !HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_9);  // ????3(PE9)????????
    case USER_BUTTON_4:
        return !HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_7);  // ????4(PE7)????????
    case USER_BUTTON_5:
        return !HAL_GPIO_ReadPin(GPIOB, GPIO_PIN_0);  // ????5(PB0)????????
    default:
        return 0; // ?????????0
    }
}

void prv_btn_event(struct ebtn_btn *btn, ebtn_evt_t evt) // ??????????????? ????:???????????,??????? ????:??
{
    if (evt == EBTN_EVT_ONPRESS) // ???????????????
    {
        switch (btn->key_id) // ???????ID???????????
        {
        case USER_BUTTON_0:
            WOUOUI_MSG_QUE_SEND(msg_down);

            // ?????????????? (KEY1)
            // ???????????????????
            sampling_init();

            if (sampling_get_state() == SAMPLING_IDLE)
            {
                // ϵͳ
                if (sampling_start() == SAMPLING_OK)
                {
                    sampling_cycle_t cycle = sampling_get_cycle();
                    my_printf(&huart1, "Periodic Sampling\r\n");
                    my_printf(&huart1, "sample cycle: %ds\r\n", (int)cycle);

                    // ò
                    extern uint8_t flag_output_active;
                    extern uint32_t last_output_timestamp;
                    flag_output_active = 1;
                    last_output_timestamp = HAL_GetTick();

                    // ¼־
                    char log_msg[64];
                    sprintf(log_msg, "sample start - cycle %ds (key1)", (int)cycle);
                    data_storage_write_log(log_msg);
                }
                else
                {
                    my_printf(&huart1, "sampling start failed.\r\n");
                }
            }
            else
            {
                // ֹͣϵͳ
                if (sampling_stop() == SAMPLING_OK)
                {
                    my_printf(&huart1, "PeriodicSamplingSTOP\r\n");

                    // ò
                    extern uint8_t flag_output_active;
                    flag_output_active = 0;

                    // ¼ֹͣ־
                    data_storage_write_log("sample stop (key1)");
                }
                else
                {
                    my_printf(&huart1, "sampling stop failed.\r\n");
                }
            }
            break;
        case USER_BUTTON_1:
         
        case USER_BUTTON_2:
            WOUOUI_MSG_QUE_SEND(msg_right);

            // ????10s???????? (KEY3)
            sampling_init();
            if (sampling_set_cycle(CYCLE_10S) == SAMPLING_OK)
            {
                my_printf(&huart1, "sample cycle adjust:10s\r\n");

                // ?????????????
                data_storage_write_log("cycle adjust to 10s (key3)");
            }
            else
            {
                my_printf(&huart1, "cycle adjust failed.\r\n");
            }
            break;
        case USER_BUTTON_3:
             WOUOUI_MSG_QUE_SEND(msg_return);

            // ????15s???????? (KEY4)
            sampling_init();
            if (sampling_set_cycle(CYCLE_15S) == SAMPLING_OK)
            {
                my_printf(&huart1, "sample cycle adjust:15s\r\n");

                // ?????????????
                data_storage_write_log("cycle adjust to 15s (key4)");
            }
            else
            {
                my_printf(&huart1, "cycle adjust failed.\r\n");
            }
            break;
        case USER_BUTTON_4:
           
        case USER_BUTTON_5:
            WOUOUI_MSG_QUE_SEND(msg_click);
            break;
        default:
            break;
        }
    }
}

void app_btn_init(void)
{
    // ebtn_init(btns, EBTN_ARRAY_SIZE(btns), btns_combo, EBTN_ARRAY_SIZE(btns_combo), prv_btn_get_state, prv_btn_event);
    ebtn_init(btns, EBTN_ARRAY_SIZE(btns), NULL, 0, prv_btn_get_state, prv_btn_event);

    //    ebtn_combo_btn_add_btn(&btns_combo[0], USER_BUTTON_0);
    //    ebtn_combo_btn_add_btn(&btns_combo[0], USER_BUTTON_1);

    //    ebtn_combo_btn_add_btn(&btns_combo[1], USER_BUTTON_2);
    //    ebtn_combo_btn_add_btn(&btns_combo[1], USER_BUTTON_3);

    HAL_TIM_Base_Start_IT(&htim14);
}

void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{
}

void btn_task(void)
{
    ebtn_process(uwTick);
}
