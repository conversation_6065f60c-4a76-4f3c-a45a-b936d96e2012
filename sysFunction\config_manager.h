// ļconfig_manager.h
// ܣùϵͳͷļṩ洢Flash־ûӿ
// ߣ״׵ӹ
// ȨCopyright (c) 2024 ״׵ӹ. All rights reserved.

#ifndef __CONFIG_MANAGER_H__
#define __CONFIG_MANAGER_H__

#include "stdint.h"
#include "sampling_control.h" // ģ

#define CONFIG_FLASH_ADDR 0x1F0000 // Flash洢ַ
#define CONFIG_MAGIC 0x43464721     // ħʶ
#define CONFIG_VERSION 0x02         // ð汾

typedef struct // òṹ
{
    uint32_t magic;              // ħʶ
    uint8_t version;             // 汾
    float ratio;                 // Ȳ
    float limit;                 // ֵ
    sampling_cycle_t cycle;      // ڲ(5s/10s/15s)
    uint32_t crc32;              // CRC32Уֵ
} config_params_t;

typedef enum // ò״̬ö
{
    CONFIG_OK = 0,           // ɹ
    CONFIG_ERROR = 1,        // ʧ
    CONFIG_INVALID = 2,      // Ч
    CONFIG_FLASH_ERROR = 3,  // Flash
    CONFIG_CRC_ERROR = 4     // CRCУ
} config_status_t;


// ùĽӿں
config_status_t config_init(void);                                      // ʼϵͳ : :״̬
config_status_t config_get_params(config_params_t *params);             // ȡò :ṹָ :״̬
config_status_t config_set_params(const config_params_t *params);       // ò :ṹָ :״̬
config_status_t config_save_to_flash(void);                             // õFlash : :״̬
config_status_t config_load_from_flash(void);                           // Flash : :״̬
config_status_t config_reset_to_default(void);                          // ΪĬ : :״̬

// ֤
config_status_t config_validate_ratio(float ratio);                     // ֤Ȳ :ֵ :֤
config_status_t config_validate_limit(float limit);                     // ֵ֤ :ֵ :֤
config_status_t config_validate_sampling_cycle(sampling_cycle_t cycle); // ֤ڲ :ֵ :֤

// ڹ
config_status_t config_set_sampling_cycle(sampling_cycle_t cycle);      // ò :ֵ :״̬
sampling_cycle_t config_get_sampling_cycle(void);                       // ȡ : :ǰ

// ߺ
uint32_t config_calculate_crc32(const config_params_t *params);         // CRC32Уֵ :ṹָ :Уֵ

#endif // __CONFIG_MANAGER_H__
