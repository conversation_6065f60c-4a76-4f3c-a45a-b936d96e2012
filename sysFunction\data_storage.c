// ݴ洢ģ data_storage.c
// ݴ洢ܣTFļ洢ݸʽ
// Ȩ ״׵ӹ
// ߣCopyright (c) 2024 ״׵ӹ. All rights reserved.

#include "data_storage.h"
#include "fatfs.h"     // FATFSļϵͳ
#include "rtc_app.h"   // RTCʱӦ
#include "usart_app.h" // Ӧ
#include "string.h"    // ַ
#include "stdio.h"     // ׼

extern uint32_t convert_rtc_to_unix_timestamp(RTC_TimeTypeDef *time, RTC_DateTypeDef *date); // ʱתusart_app.c
extern void format_hex_output(uint32_t timestamp, float measured_voltage, uint8_t is_overlimit, char *output); // ʮƸʽ

static file_state_t g_file_states[STORAGE_TYPE_COUNT]; // ļ״̬
static uint32_t g_boot_count = 0;                      // 

static const char *g_directory_names[STORAGE_TYPE_COUNT] = { // Ŀ¼
    "sample",    // STORAGE_SAMPLE
    "overLimit", // STORAGE_OVERLIMIT
    "log",       // STORAGE_LOG
    "hideData"   // STORAGE_HIDEDATA
};

static const char *g_filename_prefixes[STORAGE_TYPE_COUNT] = { // ļǰ׺
    "sampleData", // STORAGE_SAMPLE
    "overLimit",  // STORAGE_OVERLIMIT
    "log",        // STORAGE_LOG
    "hideData"    // STORAGE_HIDEDATA
};

static uint32_t get_boot_count_from_fatfs(void) // FATFSȡ : :ֵ
{
    FIL file;                // ļ
    uint32_t boot_count = 0; // 
    UINT bytes_read;         // ȡֽ
    FRESULT res;             // 

    res = f_open(&file, "boot_count.txt", FA_READ); // boot_count.txtļ
    if (res == FR_OK)                               // 򿪳ɹ
    {
        res = f_read(&file, &boot_count, sizeof(boot_count), &bytes_read); // ȡ
        if (res != FR_OK || bytes_read != sizeof(boot_count))              // ȡʧ
        {
            boot_count = 0; // ĬֵΪ0
        }
        f_close(&file); // رļ
    }
    // ļڣboot_countΪ0

    return boot_count;
}

static data_storage_status_t save_boot_count_to_fatfs(uint32_t boot_count) // FATFS :ֵ :״̬
{
    FIL file;               // ļ
    UINT bytes_written;     // дֽ
    FRESULT res;            // 

    res = f_open(&file, "boot_count.txt", FA_CREATE_ALWAYS | FA_WRITE); // boot_count.txtļ
    if (res != FR_OK)       // ʧ
    {
        return DATA_STORAGE_ERROR;
    }

    res = f_write(&file, &boot_count, sizeof(boot_count), &bytes_written); // д
    if (res != FR_OK || bytes_written != sizeof(boot_count))               // дʧ
    {
        f_close(&file);
        return DATA_STORAGE_ERROR;
    }

    f_close(&file);         // رļ
    return DATA_STORAGE_OK;
}

static data_storage_status_t create_storage_directories(void) // 洢Ŀ¼ : :״̬
{
    FRESULT res;                  // 
    uint8_t success_count = 0;    // ɹ

    for (uint8_t i = 0; i < STORAGE_TYPE_COUNT; i++) // Ŀ¼
    {
        res = f_mkdir(g_directory_names[i]);          // Ŀ¼
        if (res == FR_OK)                             // ɹ
        {
            // my_printf(&huart1, "Created directory: %s\r\n", g_directory_names[i]);
            success_count++;
        }
        else if (res == FR_EXIST) // Ŀ¼Ѵ
        {
            // my_printf(&huart1, "Directory already exists: %s\r\n", g_directory_names[i]);
            success_count++;
        }
        else // ʧ
        {
            // my_printf(&huart1, "Failed to create directory %s, error: %d\r\n", g_directory_names[i], res);
        }
    }

    return (success_count == STORAGE_TYPE_COUNT) ? DATA_STORAGE_OK : DATA_STORAGE_ERROR; // ֻĿ¼ɹŷسɹ
}

data_storage_status_t data_storage_init(void) // ʼ洢ϵͳ : :״̬
{
    memset(g_file_states, 0, sizeof(g_file_states)); // ļ״̬

    my_printf(&huart1, "Initializing data storage system...\r\n");

    FRESULT mount_result = f_mount(&SDFatFS, SDPath, 1); // SDļϵͳ
    if (mount_result != FR_OK)                           // ʧ
    {
        my_printf(&huart1, "Failed to mount SD card filesystem, error: %d\r\n", mount_result);
        my_printf(&huart1, "Data storage system will work in degraded mode\r\n");
        return DATA_STORAGE_NO_SD;
    }

    my_printf(&huart1, "SD card filesystem mounted successfully\r\n");

    data_storage_status_t dir_result = create_storage_directories(); // 洢Ŀ¼
    if (dir_result != DATA_STORAGE_OK)                               // Ŀ¼ʧ
    {
        my_printf(&huart1, "Warning: Some directories creation failed, system may not work properly\r\n");
        // ִУϵͳ޷
    }

    g_boot_count = get_boot_count_from_fatfs(); // ȡ
    g_boot_count++;

    data_storage_status_t boot_result = save_boot_count_to_fatfs(g_boot_count); // 
    if (boot_result != DATA_STORAGE_OK)                                         // ʧ
    {
        my_printf(&huart1, "Warning: Failed to save boot count\r\n");
    }

    my_printf(&huart1, "Data storage system initialized, boot count: %lu\r\n", g_boot_count); // 

    return DATA_STORAGE_OK;
}

static data_storage_status_t check_and_update_filename(storage_type_t type) // 鲢ļ :洢 :״̬
{
    if (type >= STORAGE_TYPE_COUNT) // 
    {
        return DATA_STORAGE_INVALID;
    }

    file_state_t *state = &g_file_states[type]; // ȡļ״̬

    if (state->data_count >= 10 || !state->file_exists) // Ҫļ(ÿ10ݻļ)
    {

        char filename[64];  // ļ
        data_storage_status_t result = generate_filename(type, filename); // ļ
        if (result != DATA_STORAGE_OK)
        {
            return result;
        }

        strcpy(state->current_filename, filename); // ļ
        state->data_count = 0;
        state->file_exists = 1;

        // Ϣļ׼
        // my_printf(&huart1, "DEBUG: New file prepared, type=%d, filename=%s\r\n",
        //           type, filename);
    }

    return DATA_STORAGE_OK;
}

static data_storage_status_t write_data_to_file(storage_type_t type, const char *data) // ļд :洢,ַ :״̬
{
    if (type >= STORAGE_TYPE_COUNT || data == NULL) // 
    {
        return DATA_STORAGE_INVALID;
    }

    data_storage_status_t result = check_and_update_filename(type); // 鲢ļ
    if (result != DATA_STORAGE_OK)
    {
        return result;
    }

    file_state_t *state = &g_file_states[type]; // ȡļ״̬

    // ļ·
    char full_path[96];
    sprintf(full_path, "%s/%s", g_directory_names[type], state->current_filename);

    // ļд(׷ģʽ)
    FIL file_handle;
    FRESULT res = f_open(&file_handle, full_path, FA_OPEN_ALWAYS | FA_WRITE);
    if (res != FR_OK)
    {
        // Ϣļʧ
        // my_printf(&huart1, "DEBUG: File open failed, type=%d, path=%s, res=%d\r\n",
        //           type, full_path, res);
        return DATA_STORAGE_ERROR;
    }

    // ƶļĩβ(׷ģʽ)
    res = f_lseek(&file_handle, f_size(&file_handle));
    if (res != FR_OK)
    {
        f_close(&file_handle);
        return DATA_STORAGE_ERROR;
    }

    UINT bytes_written;                                                           // дֽ
    res = f_write(&file_handle, data, strlen(data), &bytes_written);             // д
    if (res != FR_OK || bytes_written != strlen(data))                           // дʧ
    {
        // Ϣдʧ
        // my_printf(&huart1, "DEBUG: File write failed, type=%d, res=%d, expected=%d, written=%d\r\n",
        //           type, res, strlen(data), bytes_written);
        f_close(&file_handle);
        return DATA_STORAGE_ERROR;
    }

    res = f_write(&file_handle, "\n", 1, &bytes_written); // д뻻з
    if (res != FR_OK || bytes_written != 1)               // здʧ
    {
        f_close(&file_handle);
        return DATA_STORAGE_ERROR;
    }

    f_sync(&file_handle); // ͬݵSD
    f_close(&file_handle); // رļ

    state->data_count++; // ݼ

    // Ϣдɹ
    // my_printf(&huart1, "DEBUG: Data written successfully, type=%d, path=%s, count=%d\r\n",
    //           type, full_path, state->data_count);

    return DATA_STORAGE_OK;
}


static data_storage_status_t compose_sample_string(float measured_voltage, char *formatted_data)
{
    if (formatted_data == NULL)
    {
        return DATA_STORAGE_INVALID;
    }


    RTC_TimeTypeDef current_rtc_time = {0};
    RTC_DateTypeDef current_rtc_date = {0};
    HAL_RTC_GetTime(&hrtc, &current_rtc_time, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &current_rtc_date, RTC_FORMAT_BIN);


    sprintf(formatted_data, "%04d-%02d-%02d %02d:%02d:%02d %.1fV",
            current_rtc_date.Year + 2000,
            current_rtc_date.Month,
            current_rtc_date.Date,
            current_rtc_time.Hours,
            current_rtc_time.Minutes,
            current_rtc_time.Seconds,
            measured_voltage);

    return DATA_STORAGE_OK;
}

data_storage_status_t data_storage_write_sample(float measured_voltage) // д :ѹֵ :״̬
{
    char formatted_data[128]; // ʽݻ

    data_storage_status_t result = compose_sample_string(measured_voltage, formatted_data); // ʽ
    if (result != DATA_STORAGE_OK)
    {
        return result;
    }

    return write_data_to_file(STORAGE_SAMPLE, formatted_data); // дļ
}


static data_storage_status_t compose_overlimit_string(float measured_voltage, float limit, char *formatted_data)
{
    if (formatted_data == NULL)
    {
        return DATA_STORAGE_INVALID;
    }


    RTC_TimeTypeDef current_rtc_time = {0};
    RTC_DateTypeDef current_rtc_date = {0};
    HAL_RTC_GetTime(&hrtc, &current_rtc_time, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &current_rtc_date, RTC_FORMAT_BIN);


    sprintf(formatted_data, "%04d-%02d-%02d %02d:%02d:%02d %.0fV limit %.0fV",
            current_rtc_date.Year + 2000,
            current_rtc_date.Month,
            current_rtc_date.Date,
            current_rtc_time.Hours,
            current_rtc_time.Minutes,
            current_rtc_time.Seconds,
            measured_voltage,
            limit);

    return DATA_STORAGE_OK;
}

data_storage_status_t data_storage_write_overlimit(float measured_voltage, float limit) // д볬 :ѹֵ,ֵ :״̬
{
    char formatted_data[128]; // ʽݻ

    data_storage_status_t result = compose_overlimit_string(measured_voltage, limit, formatted_data); // ʽ
    if (result != DATA_STORAGE_OK)
    {
        return result;
    }

    return write_data_to_file(STORAGE_OVERLIMIT, formatted_data); // дļ
}


static data_storage_status_t compose_log_string(const char *operation, char *formatted_data)
{
    if (formatted_data == NULL || operation == NULL)
    {
        return DATA_STORAGE_INVALID;
    }

    RTC_TimeTypeDef current_rtc_time = {0};
    RTC_DateTypeDef current_rtc_date = {0};
    HAL_RTC_GetTime(&hrtc, &current_rtc_time, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &current_rtc_date, RTC_FORMAT_BIN);

    sprintf(formatted_data, "%04d-%02d-%02d %02d:%02d:%02d %s",
            current_rtc_date.Year + 2000,
            current_rtc_date.Month,
            current_rtc_date.Date,
            current_rtc_time.Hours,
            current_rtc_time.Minutes,
            current_rtc_time.Seconds,
            operation);

    return DATA_STORAGE_OK;
}

data_storage_status_t data_storage_write_log(const char *operation) // д־ : :״̬
{
    char formatted_data[256]; // ʽݻ

    data_storage_status_t result = compose_log_string(operation, formatted_data); // ʽ
    if (result != DATA_STORAGE_OK)
    {
        return result;
    }

    return write_data_to_file(STORAGE_LOG, formatted_data); // дļ
}


static data_storage_status_t compose_hidden_data_string(float measured_voltage, uint8_t is_overlimit, char *formatted_data)
{
    if (formatted_data == NULL)
    {
        return DATA_STORAGE_INVALID;
    }


    RTC_TimeTypeDef current_rtc_time = {0};
    RTC_DateTypeDef current_rtc_date = {0};
    HAL_RTC_GetTime(&hrtc, &current_rtc_time, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &current_rtc_date, RTC_FORMAT_BIN);


    char original_line[128];
    sprintf(original_line, "%04d-%02d-%02d %02d:%02d:%02d %.1fV",
            current_rtc_date.Year + 2000,
            current_rtc_date.Month,
            current_rtc_date.Date,
            current_rtc_time.Hours,
            current_rtc_time.Minutes,
            current_rtc_time.Seconds,
            measured_voltage);

    
    uint32_t timestamp = convert_rtc_to_unix_timestamp(&current_rtc_time, &current_rtc_date);
    char hex_output[32];
    format_hex_output(timestamp, measured_voltage, is_overlimit, hex_output);

    sprintf(formatted_data, "%s\nhide: %s", original_line, hex_output);

    return DATA_STORAGE_OK;
}

data_storage_status_t data_storage_write_hidedata(float measured_voltage, uint8_t is_overlimit) // д :ѹֵ,ޱ־ :״̬
{
    char formatted_data[256]; // ʽݻ

    data_storage_status_t result = compose_hidden_data_string(measured_voltage, is_overlimit, formatted_data); // ʽ
    if (result != DATA_STORAGE_OK)
    {
        return result;
    }

    return write_data_to_file(STORAGE_HIDEDATA, formatted_data); // дhideDataĿ¼ĺʮ2ָʽ
}

data_storage_status_t generate_datetime_string(char *datetime_str) // datetimeַ :ַ :״̬
{
    if (datetime_str == NULL) // 
    {
        return DATA_STORAGE_INVALID;
    }

    RTC_TimeTypeDef current_rtc_time = {0}; // ȡǰRTCʱ
    RTC_DateTypeDef current_rtc_date = {0};
    HAL_RTC_GetTime(&hrtc, &current_rtc_time, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &current_rtc_date, RTC_FORMAT_BIN);

    sprintf(datetime_str, "%04d%02d%02d%02d%02d%02d", // ʽYYYYMMDDHHMMSSʽ(14λ)
            current_rtc_date.Year + 2000,             // 4λ
            current_rtc_date.Month,
            current_rtc_date.Date,
            current_rtc_time.Hours,
            current_rtc_time.Minutes,
            current_rtc_time.Seconds);

    return DATA_STORAGE_OK;
}

data_storage_status_t generate_filename(storage_type_t type, char *filename) // ļ :洢,ļ :״̬
{
    if (filename == NULL || type >= STORAGE_TYPE_COUNT) // 
    {
        return DATA_STORAGE_INVALID;
    }

    if (type == STORAGE_LOG) // logʹlog{boot_count}.txt
    {
        sprintf(filename, "%s%lu.txt", g_filename_prefixes[type], g_boot_count);
    }
    else // ʹdatetime{prefix}{datetime}.txt
    {
        char datetime_str[16];                                                  // ʱַ
        data_storage_status_t result = generate_datetime_string(datetime_str); // ʱַ
        if (result != DATA_STORAGE_OK)
        {
            return result;
        }
        sprintf(filename, "%s%s.txt", g_filename_prefixes[type], datetime_str);
    }

    return DATA_STORAGE_OK;
}

data_storage_status_t data_storage_test(void) // Դ洢ϵͳ : :״̬
{
    my_printf(&huart1, "Data storage system test - placeholder\r\n");
    return DATA_STORAGE_OK;
}
