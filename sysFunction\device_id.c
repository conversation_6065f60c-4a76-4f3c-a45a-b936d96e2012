// ļdevice_id.c
// ܣ豸IDϵͳṩ豸IDĶȡд͹
// ߣ״׵ӹ
// ȨCopyright (c) 2024 ״׵ӹ. All rights reserved.

#include "device_id.h"
#include "gd25qxx.h"   // Flashӿ
#include "usart_app.h" // Ӧ
#include "string.h"
#include "stdio.h"

static char g_device_id[DEVICE_ID_MAX_LENGTH] = {0}; // 豸ID

// Ĭ豸ID - ޸ĶID
#define DEFAULT_DEVICE_ID "Device_ID:2025-CIMC-2025129954"

// ǿñ־ - Ϊ1ǿдFlashе豸ID
#define FORCE_RESET_DEVICE_ID 1

device_id_status_t device_id_init(void) // ʼ豸IDϵͳ
{
    char device_id[DEVICE_ID_MAX_LENGTH];
    device_id_status_t status;

#if FORCE_RESET_DEVICE_ID
    // ǿģʽֱдµĬID
    status = device_id_set_default();
    if (status != DEVICE_ID_OK) {
        my_printf(&huart1, "Failed to force reset device ID\r\n");
        return status;
    }

    // ȡ֤
    status = device_id_read(device_id);
    if (status != DEVICE_ID_OK) {
        my_printf(&huart1, "Failed to read after force reset\r\n");
        return status;
    }
#else
    // ģʽԴFlashȡ豸ID
    status = device_id_read(device_id);

    if (status != DEVICE_ID_OK)
    {
        // ȡʧܣĬ豸ID
        status = device_id_set_default();
        if (status != DEVICE_ID_OK)
        {
            return status;
        }

        // ¶ȡ֤
        status = device_id_read(device_id);
        if (status != DEVICE_ID_OK)
        {
            return status;
        }
    }
#endif

    // 豸ID
    strcpy(g_device_id, device_id);

    return DEVICE_ID_OK;
}

device_id_status_t device_id_read(char *device_id) // Flashȡ豸ID
{
    if (device_id == NULL)
    {
        return DEVICE_ID_INVALID;
    }

    uint8_t buffer[DEVICE_ID_MAX_LENGTH];

    // Flashȡ
    spi_flash_buffer_read(buffer, DEVICE_ID_FLASH_ADDR, DEVICE_ID_MAX_LENGTH);


    // ǷΪЧ豸ID"Device_ID:"ͷ
    if (strncmp((char *)buffer, "Device_ID:", 10) != 0)
    {
        my_printf(&huart1, "Device ID not found in Flash\r\n");
        return DEVICE_ID_NOT_FOUND;
    }

    // 豸IDȷַ
    strncpy(device_id, (char *)buffer, DEVICE_ID_MAX_LENGTH - 1);
    device_id[DEVICE_ID_MAX_LENGTH - 1] = '\0'; // ȷַ


    return DEVICE_ID_OK;
}

device_id_status_t device_id_write(const char *device_id) // д豸IDFlash
{
    if (device_id == NULL || strlen(device_id) >= DEVICE_ID_MAX_LENGTH)
    {
        return DEVICE_ID_INVALID;
    }

    uint8_t buffer[DEVICE_ID_MAX_LENGTH];
    memset(buffer, 0, sizeof(buffer));

    // 豸ID
    strncpy((char *)buffer, device_id, DEVICE_ID_MAX_LENGTH - 1);

    // Flash
    spi_flash_sector_erase(DEVICE_ID_FLASH_ADDR);

    // д豸IDFlash
    spi_flash_buffer_write(buffer, DEVICE_ID_FLASH_ADDR, DEVICE_ID_MAX_LENGTH);

    // »
    strcpy(g_device_id, device_id);

    return DEVICE_ID_OK;
}

device_id_status_t device_id_set_default(void) // Ĭ豸ID
{
    return device_id_write(DEFAULT_DEVICE_ID);
}

void device_id_print_startup_info(void) // ӡ豸IDϢ
{
    my_printf(&huart1, "====system init====\r\n");

    if (strlen(g_device_id) > 0)
    {
        my_printf(&huart1, "%s\r\n", g_device_id);
    }
    else
    {
        my_printf(&huart1, "%s\r\n", DEFAULT_DEVICE_ID);
    }

    my_printf(&huart1, "====system ready====\r\n");
}
