#ifndef __DEVICE_ID_H
#define __DEVICE_ID_H

#include "mydefine.h"

// 豸IDس
#define DEVICE_ID_MAX_LENGTH 32  // 豸ID󳤶
#define DEVICE_ID_FLASH_ADDR 0x0000  // Flashд洢豸IDĵַ

// 豸ID״̬ö
typedef enum {
    DEVICE_ID_OK = 0,
    DEVICE_ID_ERROR,
    DEVICE_ID_NOT_FOUND,
    DEVICE_ID_INVALID
} device_id_status_t;

// 
device_id_status_t device_id_init(void);  // ʼ豸IDϵͳ
device_id_status_t device_id_read(char *device_id);  // Flashȡ豸ID
device_id_status_t device_id_write(const char *device_id);  // д豸IDFlash
device_id_status_t device_id_set_default(void);  // Ĭ豸ID
void device_id_print_startup_info(void);  // ӡ豸IDϢ

#endif // __DEVICE_ID_H
