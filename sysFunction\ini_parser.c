// ļini_parser.c
// ܣINIļʵ֣֧config.iniʽͲȡ
// ߣ״׵ӹ
// ȨCopyright (c) 2024 ״׵ӹ. All rights reserved.

#include "ini_parser.h"
#include "string.h" // ַ
#include "stdlib.h" // ׼⺯
#include "ctype.h"  // ַж

typedef enum // ״̬ö
{
    PARSE_IDLE = 0,  // ״̬
    PARSE_RATIO = 1, // Ratio
    PARSE_LIMIT = 2  // Limit
} parse_state_t;

ini_status_t ini_trim_string(char *str) // ȥַβո :ַָ :״̬
{
    if (str == NULL) // 
        return INI_ERROR;

    char *start = str; // ȥǰո
    while (*start && isspace(*start))
        start++;

    char *end = start + strlen(start) - 1; // ȥβո
    while (end > start && isspace(*end))
        end--;
    *(end + 1) = '\0';

    if (start != str) // ƶַͷ
    {
        memmove(str, start, strlen(start) + 1);
    }

    return INI_OK;
}

ini_status_t ini_parse_float(const char *str, float *value) //  :ַ,ָ :״̬
{
    if (str == NULL || value == NULL) // 
        return INI_ERROR;

    char *endptr;                      // תλָ
    *value = strtof(str, &endptr);     // ַת

    if (endptr == str || *endptr != '\0') // תǷɹ
    {
        return INI_VALUE_ERROR;
    }

    return INI_OK;
}

ini_status_t ini_parse_line(const char *line, ini_config_t *config) //  :ַ,ýṹָ :״̬
{
    if (line == NULL || config == NULL) // 
        return INI_ERROR;

    static parse_state_t current_state = PARSE_IDLE; // ̬״̬
    char line_buffer[128];                           // л

    strncpy(line_buffer, line, sizeof(line_buffer) - 1); // ݵ
    line_buffer[sizeof(line_buffer) - 1] = '\0';

    ini_trim_string(line_buffer); // ȥβո

    if (strlen(line_buffer) == 0 || line_buffer[0] == ';' || line_buffer[0] == '#') // кע
    {
        return INI_OK;
    }

    if (line_buffer[0] == '[') // ǷΪα
    {
        char *end_bracket = strchr(line_buffer, ']'); // ҽ
        if (end_bracket == NULL)
            return INI_FORMAT_ERROR;

        *end_bracket = '\0';                      // ضַ
        char *section_name = line_buffer + 1;    // ȡ
        ini_trim_string(section_name);           // ȥո

        if (strcmp(section_name, "Ratio") == 0) // ж϶
        {
            current_state = PARSE_RATIO; // Ratio
        }
        else if (strcmp(section_name, "Limit") == 0)
        {
            current_state = PARSE_LIMIT; // Limit
        }
        else
        {
            current_state = PARSE_IDLE; // δ֪Σ״̬
        }

        return INI_OK;
    }

    char *equal_sign = strchr(line_buffer, '='); // ֵԣҵȺ
    if (equal_sign == NULL)
        return INI_FORMAT_ERROR;

    *equal_sign = '\0';               // ֵָ
    char *key = line_buffer;          // 
    char *value = equal_sign + 1;     // ֵ

    ini_trim_string(key);             // ȥո
    ini_trim_string(value);           // ȥֵո

    if (strcmp(key, "Ch0") == 0)      // ǷΪCh0
    {
        float parsed_value;           // ֵ
        if (ini_parse_float(value, &parsed_value) != INI_OK)
        {
            return INI_VALUE_ERROR;
        }

        if (current_state == PARSE_RATIO) // ݵǰ״̬öӦ
        {
            config->ratio = parsed_value; // ñȲ
            config->ratio_found = 1;      // ҵratio
        }
        else if (current_state == PARSE_LIMIT)
        {
            config->limit = parsed_value; // ֵ
            config->limit_found = 1;      // ҵlimit
        }
    }

    return INI_OK;
}

ini_status_t ini_parse_file(const char *filename, ini_config_t *config) // INIļ :ļ,ýṹָ :״̬
{
    if (filename == NULL || config == NULL) // 
        return INI_ERROR;

    FIL file;                // FATFSļ
    FRESULT fr;              // ļ
    char line_buffer[128];   // л
    UINT bytes_read;         // ȡֽ

    config->ratio = 0.0f;    // ʼýṹ
    config->limit = 0.0f;
    config->ratio_found = 0;
    config->limit_found = 0;

    fr = f_open(&file, filename, FA_READ); // ļ
    if (fr != FR_OK)
    {
        return INI_FILE_NOT_FOUND;
    }

    while (f_gets(line_buffer, sizeof(line_buffer), &file) != NULL) // жȡļ
    {
        ini_status_t status = ini_parse_line(line_buffer, config); // ÿһ
        if (status != INI_OK)                                      // ʧܴ
        {
            f_close(&file);
            return status;
        }
    }

    f_close(&file); // رļ

    return INI_OK;
}
