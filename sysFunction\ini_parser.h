// ļini_parser.h
// ܣINIļͷļṩļͲȡӿ
// ߣ״׵ӹ
// ȨCopyright (c) 2024 ״׵ӹ. All rights reserved.

#ifndef __INI_PARSER_H__
#define __INI_PARSER_H__

#include "stdint.h" // ׼Ͷ
#include "ff.h"     // FATFSļϵͳ

typedef enum // INI״̬ö
{
    INI_OK = 0,             // ɹ
    INI_ERROR = 1,          // һ
    INI_FILE_NOT_FOUND = 2, // ļ
    INI_FORMAT_ERROR = 3,   // ʽ
    INI_VALUE_ERROR = 4     // ֵת
} ini_status_t;

typedef struct // INIݽṹ
{
    float ratio;         // Ȳ
    float limit;         // ֵ
    uint8_t ratio_found; // Ƿҵratio
    uint8_t limit_found; // Ƿҵlimit
} ini_config_t;

// INIĽӿ
ini_status_t ini_parse_file(const char *filename, ini_config_t *config); // INIļ :ļ,ýṹָ :״̬
ini_status_t ini_parse_line(const char *line, ini_config_t *config);     //  :ַ,ýṹָ :״̬

// ߺ
ini_status_t ini_trim_string(char *str);                     // ȥַβո :ַָ :״̬
ini_status_t ini_parse_float(const char *str, float *value); //  :ַ,ָ :״̬

#endif // __INI_PARSER_H__
