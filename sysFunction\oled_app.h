// ļoled_app.h
// ܣOLEDʾӦͷļṩʾʵʱչʾӿ
// ߣ״׵ӹ
// ȨCopyright (c) 2024 ״׵ӹ. All rights reserved.

#ifndef __OLED_APP_H__
#define __OLED_APP_H__

#include "mydefine.h" // ȫֶͷļ

int oled_display_output(uint8_t x, uint8_t y, const char *format, ...);                                    // OLEDʽ :X,Y,ʽַ,ɱ :ַ
void oled_task(void);                                                                              // OLED : :
uint8_t u8g2_gpio_and_delay_stm32(u8x8_t *u8x8, uint8_t msg, uint8_t arg_int, void *arg_ptr);    // U8G2 GPIOʱص :U8X8ṹ,Ϣ,Ͳ,ָ :
uint8_t u8x8_byte_hw_i2c(u8x8_t *u8x8, uint8_t msg, uint8_t arg_int, void *arg_ptr);             // U8X8ӲI2Cֽڴ :U8X8ṹ,Ϣ,Ͳ,ָ :
void OLED_SendBuff(uint8_t buff[4][128]);                                                          // OLEDͻ :4x128 :
void PIDMenu_Init(void);                                                                           // PID˵ʼ : :
#endif
