#ifndef __RTC_APP_H__
#define __RTC_APP_H__

#include "mydefine.h"

// RTC
void rtc_proc(void); // ԭеRTC

// ʱúͲѯ
HAL_StatusTypeDef rtc_set_time_from_string(const char *time_str); // ַʱ
void rtc_show_time_serial(void);                                // ӡǰʱ

// չܣΪԤ
void rtc_get_time_info(RTC_TimeTypeDef *current_time, RTC_DateTypeDef *current_date);                                  // ȡʱϢ
void format_time_output(const RTC_TimeTypeDef *sTime, const RTC_DateTypeDef *sDate, char *buffer, size_t buffer_size); // ʽʱ

#endif
