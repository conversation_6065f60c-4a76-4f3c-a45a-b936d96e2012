// ļsampling_control.c
// ܣϵͳʵ֣ṩݲɼƺ״̬
// ߣ״׵ӹ
// ȨCopyright (c) 2024 ״׵ӹ. All rights reserved.

#include "sampling_control.h"
#include "stddef.h"
#include "config_manager.h" // ùϵͳ
#include "data_storage.h"   // ݴ洢ϵͳ
#include "usart_app.h"      // Ӧãڻȡʽ
#include "adc_app.h"        // ADCӦģ

static sampling_control_t g_sampling_control = {0}; // ȫֲʵ
static uint8_t g_sampling_initialized = 0;          // ʼ־

#define LED_BLINK_PERIOD_MS 1000 // LED˸ڶ(1=1000ms)

sampling_status_t sampling_init(void) // ʼϵͳ : :״̬
{
    if (g_sampling_initialized) // Ƿѳʼ
    {
        return SAMPLING_OK;
    }

    config_init(); // ʼùϵͳ

    g_sampling_control.state = SAMPLING_IDLE;                       // óʼ״̬Ϊ
    g_sampling_control.cycle = config_get_sampling_cycle();         // ϵͳ
    g_sampling_control.last_sample_time = 0;                        // ϴβʱ
    g_sampling_control.led_blink_time = 0;                          // LED˸ʱ
    g_sampling_control.led_blink_state = 0;                         // LED˸״̬

    g_sampling_initialized = 1; // óʼ־
    return SAMPLING_OK;
}

sampling_status_t sampling_start(void) //  : :״̬
{
    if (!g_sampling_initialized) // ʼ״̬
        return SAMPLING_ERROR;

    g_sampling_control.state = SAMPLING_ACTIVE;                     // Ϊ״̬
    g_sampling_control.last_sample_time = HAL_GetTick();            // ¼ʱ
    g_sampling_control.led_blink_time = HAL_GetTick();              // ¼LED˸ʱ
    g_sampling_control.led_blink_state = 0;                         // ʼLED״̬

    return SAMPLING_OK;
}

sampling_status_t sampling_stop(void) // ֹͣ : :״̬
{
    if (!g_sampling_initialized) // ʼ״̬
        return SAMPLING_ERROR;

    g_sampling_control.state = SAMPLING_IDLE;       // Ϊ״̬
    g_sampling_control.led_blink_state = 0;         // LEDϨ

    return SAMPLING_OK;
}

sampling_status_t sampling_set_cycle(sampling_cycle_t cycle) // ò :ֵ :״̬
{
    if (!g_sampling_initialized) // ʼ״̬
        return SAMPLING_ERROR;

    if (cycle != CYCLE_5S && cycle != CYCLE_10S && cycle != CYCLE_15S) // ֤ڲ
    {
        return SAMPLING_INVALID;
    }

    g_sampling_control.cycle = cycle; // ±

    if (config_set_sampling_cycle(cycle) == CONFIG_OK) // 浽ùϵͳдFlash
    {
        config_save_to_flash();
    }

    return SAMPLING_OK;
}

sampling_state_t sampling_get_state(void) // ȡ״̬ : :ǰ״̬
{
    if (!g_sampling_initialized) // ʼ״̬
        return SAMPLING_IDLE;
    return g_sampling_control.state;
}

sampling_cycle_t sampling_get_cycle(void) // ȡ : :ǰ
{
    if (!g_sampling_initialized) // ʼ״̬
        return CYCLE_5S;
    return g_sampling_control.cycle;
}

uint8_t sampling_should_sample(void) // ǷӦò : :־
{
    if (!g_sampling_initialized || g_sampling_control.state != SAMPLING_ACTIVE) // ʼ״̬
    {
        return 0;
    }

    uint32_t current_time = HAL_GetTick();                                       // ȡǰʱ
    uint32_t elapsed_time = current_time - g_sampling_control.last_sample_time; // ʱ
    uint32_t cycle_ms = g_sampling_control.cycle * 1000;                        // תΪ

    return (elapsed_time >= cycle_ms) ? 1 : 0; // жǷ񵽴ʱ
}

void sampling_update_led_blink(void) // LED˸״̬ : :
{
    if (!g_sampling_initialized || g_sampling_control.state != SAMPLING_ACTIVE) // ʼ״̬
    {
        g_sampling_control.led_blink_state = 0; // ֹͣʱLEDϨ
        return;
    }

    uint32_t current_time = HAL_GetTick();                                      // ȡǰʱ
    uint32_t elapsed_time = current_time - g_sampling_control.led_blink_time;  // ˸

    if (elapsed_time >= LED_BLINK_PERIOD_MS) // Ƿ񵽴˸
    {
        g_sampling_control.led_blink_state ^= 1;            // תLED״̬
        g_sampling_control.led_blink_time = current_time;   // ˸ʱ
    }
}

float sampling_get_voltage(void) // ȡǰѹֵ : :ѹֵ
{
    extern __IO float measured_voltage;     // adc_app.cȫֵѹ
    config_params_t config_params; // òṹ

    if (config_get_params(&config_params) != CONFIG_OK) // ȡò
    {
        return measured_voltage; // ûȡʧʱԭʼѹֵ
    }

    return measured_voltage * config_params.ratio; // ʹratioеѹ
}

uint8_t sampling_check_overlimit(void) // Ƿ : :ޱ־
{
    config_params_t config_params; // òṹ

    if (config_get_params(&config_params) != CONFIG_OK) // ȡò
    {
        return 0; // ûȡʧʱΪδ
    }

    float current_voltage = sampling_get_voltage(); // ȡǰѹֵ

    return (current_voltage > config_params.limit) ? 1 : 0; // Ƿ񳬹limitֵ
}

void sampling_task(void) //  : :
{
    if (!g_sampling_initialized) // ʼ״̬
        return;

    sampling_update_led_blink(); // LED˸״̬

    if (g_sampling_control.state == SAMPLING_ACTIVE) // ڲ״̬ǷҪв
    {
        if (sampling_should_sample()) // Ƿ񵽴ʱ
        {
            g_sampling_control.last_sample_time = HAL_GetTick(); // ²ʱ

            float current_voltage = sampling_get_voltage();      // ȡǰѹֵ
            uint8_t is_overlimit = sampling_check_overlimit();   // Ƿ

            // ݴ洢
            sampling_handle_data_storage(current_voltage, is_overlimit);
        }
    }
}

uint8_t sampling_get_led_blink_state(void) // ȡLED˸״̬ : :˸״̬
{
    if (!g_sampling_initialized) // ʼ״̬
        return 0;
    return g_sampling_control.led_blink_state;
}

void sampling_handle_data_storage(float measured_voltage, uint8_t is_overlimit) // ݴ洢 :ѹֵ,ޱ־ :
{
    // ȡǰʽ
    extern output_format_t g_output_format; // usart_app.cȫֱ

    // ݴ洢ʽѡ洢ʽ
    if (g_output_format == OUTPUT_FORMAT_HIDDEN)
    {
        // ģʽ洢hideDataļ
        data_storage_status_t result = data_storage_write_hidedata(measured_voltage, is_overlimit);
        if (result != DATA_STORAGE_OK)
        {
            // 洢ʧʱϢ
            // Ӵ߼
        }
    }
    else
    {
        // ģʽ洢sampleļ
        data_storage_status_t result = data_storage_write_sample(measured_voltage);
        if (result != DATA_STORAGE_OK)
        {
            // 洢ʧʱϢ
            // Ӵ߼
        }
    }

    // ݶ洢overLimitļУhideģʽ
    if (is_overlimit)
    {
        config_params_t config_params;
        float limit_value = 0.0f;

        // ȡlimitֵڴ洢
        if (config_get_params(&config_params) == CONFIG_OK)
        {
            limit_value = config_params.limit;
        }

        data_storage_status_t result = data_storage_write_overlimit(measured_voltage, limit_value);
        if (result != DATA_STORAGE_OK)
        {
            // 洢ʧʱϢ
            // Ӵ߼
        }
    }
}
