#include "scheduler.h"

// ļscheduler.c
// ܣʵ֣ʱƬѯĶϵͳ
// ߣ״׵ӹ
// ȨCopyright (c) 2024 ״׵ӹ. All rights reserved.

uint8_t task_num; // 

typedef struct // ṹ嶨
{
    void (*task_func)(void); // ָ
    uint32_t rate_ms;        // ִ()
    uint32_t last_run;       // ϴִʱ
} task_t;

// ϵͳбҪȵִ
static task_t scheduler_task[] =
    {
        {led_task, 1, 0},      // LED1ms
        {adc_task, 100, 0},    // ADC100ms
        {btn_task, 5, 0},      // 5ms
        {uart_task, 5, 0},     // ͨ5ms
        {oled_task, 1, 0},     // OLEDʾ1ms
        {sampling_task, 10, 0} // 10ms
};

void scheduler_init(void) // ʼ : :
{
    task_num = sizeof(scheduler_task) / sizeof(task_t); // 
}

void scheduler_run(void) // ѭ : :
{
    for (uint8_t i = 0; i < task_num; i++) // 
    {
        uint32_t now_time = HAL_GetTick(); // ȡǰϵͳʱ

        if (now_time >= scheduler_task[i].rate_ms + scheduler_task[i].last_run) // Ƿ񵽴ִʱ
        {
            scheduler_task[i].last_run = now_time; // ϴִʱ
            scheduler_task[i].task_func();         // ִ
        }
    }
}
