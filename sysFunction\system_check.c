#include "system_check.h"
#include "gd25qxx.h" // Flashӿ
#include "ff.h"      // FATFSļϵͳ
#include "fatfs.h"   // FATFS
#include "rtc_app.h" // RTCӦýӿ
#include "diskio.h"  // IOӿ

// Copyright (c) 2024 ״׵ӹ. All rights reserved.

// Flashͺʶ - չ
typedef struct
{
    uint32_t id;
    const char *model;
    uint32_t capacity_mb;
} flash_model_t;

static const flash_model_t flash_models[] = {
    {0xC84017, "GD25Q64", 8}, // 8MB
    {0xC84016, "GD25Q32", 4}, // 4MB
    {0xC84015, "GD25Q16", 2}, // 2MB
    {0xC84014, "GD25Q80", 1}, // 1MB
    {0xC84013, "GD25Q40", 1}, // 512KB = 0.5MBʾΪ1MB
    {0xEF4017, "W25Q64", 8},  // Winbond 8MB
    {0xEF4016, "W25Q32", 4},  // Winbond 4MB
    {0xEF4015, "W25Q16", 2},  // Winbond 2MB
    {0x000000, "Unknown", 0}  // δ֪ͺ
};

// Flash IDȡͺϢ
const char *get_flash_model_name(uint32_t flash_id)
{
    for (int i = 0; flash_models[i].id != 0x000000; i++)
    {
        if (flash_models[i].id == flash_id)
        {
            return flash_models[i].model;
        }
    }
    return "Unknown";
}

// ȡFlash
static uint32_t get_flash_capacity(uint32_t flash_id)
{
    for (int i = 0; flash_models[i].id != 0x000000; i++)
    {
        if (flash_models[i].id == flash_id)
        {
            return flash_models[i].capacity_mb;
        }
    }
    return 0;
}

// ȡSD(KB)
static uint32_t sd_card_capacity_get(void)
{
    DWORD sector_count = 0;
    WORD sector_size = 0;

    // ȡ
    if (disk_ioctl(0, GET_SECTOR_COUNT, &sector_count) == RES_OK)
    {
        // ȡС
        if (disk_ioctl(0, GET_SECTOR_SIZE, &sector_size) == RES_OK)
        {
            // (KB) =  * С / 1024
            return (uint32_t)((uint64_t)sector_count * sector_size / 1024);
        }
    }
    return 0;
}

// Flash״̬
system_check_status_t check_storage_flash(flash_info_t *flash_info)
{
    if (flash_info == NULL)
    {
        return SYSTEM_CHECK_ERROR;
    }

    // ȡFlash ID
    flash_info->flash_id = spi_flash_read_id();

    // ǷΪЧID
    if (flash_info->flash_id == 0x000000 || flash_info->flash_id == 0xFFFFFF)
    {
        flash_info->status = SYSTEM_CHECK_NOT_FOUND;
        strcpy(flash_info->model_name, "Not Found");
        flash_info->capacity_mb = 0;
        return SYSTEM_CHECK_NOT_FOUND;
    }

    // ȡͺźϢ
    const char *model = get_flash_model_name(flash_info->flash_id);
    strcpy(flash_info->model_name, model);
    flash_info->capacity_mb = get_flash_capacity(flash_info->flash_id);
    flash_info->status = SYSTEM_CHECK_OK;

    return SYSTEM_CHECK_OK;
}

// SD״̬
system_check_status_t check_storage_tf(sd_card_info_t *sd_info)
{
    if (sd_info == NULL)
    {
        return SYSTEM_CHECK_ERROR;
    }

    // ʹdisk_initializeSD
    DSTATUS sd_status = disk_initialize(0);
    if (sd_status == 0)
    {
        // SDʼɹȡϢ
        uint32_t capacity_kb = sd_card_capacity_get();

        sd_info->capacity_mb = capacity_kb / 1024; // תΪMB
        sd_info->sector_size = 512;                // ׼С
        sd_info->sector_count = capacity_kb * 2;   // KBתΪ(512ֽ/)
        sd_info->status = SYSTEM_CHECK_OK;

        return SYSTEM_CHECK_OK;
    }
    else
    {
        // SDʧ
        sd_info->status = SYSTEM_CHECK_NOT_FOUND;
        sd_info->capacity_mb = 0;
        sd_info->sector_count = 0;
        sd_info->sector_size = 0;

        return SYSTEM_CHECK_NOT_FOUND;
    }
}

// ӡRTCʱ
void print_rtc_overview(void)
{
    rtc_show_time_serial(); // RTCӦģʱӡ
}

// ӡϵͳϢ - ʽ
void display_sys_info(const system_info_t *info)
{
    my_printf(&huart1, "-------system selftest-------\r\n");

    // Flash
    if (info->flash_info.status == SYSTEM_CHECK_OK)
    {
        my_printf(&huart1, "flash......ok\r\n");
    }
    else
    {
        my_printf(&huart1, "flash......error\r\n");
    }

    // SD
    if (info->sd_info.status == SYSTEM_CHECK_OK)
    {
        my_printf(&huart1, "TF card......ok\r\n");
        // KBʾ
        uint32_t capacity_kb = info->sd_info.capacity_mb * 1024;
        my_printf(&huart1, "TF card memory: %d KB\r\n", capacity_kb);
    }
    else
    {
        my_printf(&huart1, "TF card.......error\r\n");
        my_printf(&huart1, "can not find TF card\r\n");
    }

    // ʾFlash ID
    my_printf(&huart1, "flash ID:0x%06X\r\n", info->flash_info.flash_id);

    // RTCʱ
    my_printf(&huart1, "RTC Time: ");
    print_rtc_overview();

    my_printf(&huart1, "-------system selftest-------\r\n");
}

// ϵͳԼ
void perform_sys_diagnostics(void)
{
    system_info_t system_info = {0}; // ʼϵͳϢṹ

    // ִи
    check_storage_flash(&system_info.flash_info);
    check_storage_tf(&system_info.sd_info);
    system_info.rtc_status = SYSTEM_CHECK_OK; // RTC״̬ĬOK

    // ӡ
    display_sys_info(&system_info);
}
