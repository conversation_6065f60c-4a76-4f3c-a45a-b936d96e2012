// ļsystem_check.h
// ܣϵͳԼģͷļṩӲ豸״̬ѯӿ
// ߣ״׵ӹ
// ȨCopyright (c) 2024 ״׵ӹ. All rights reserved.

#ifndef __SYSTEM_CHECK_H__
#define __SYSTEM_CHECK_H__

#include "mydefine.h" // ȫֶͷļ

typedef enum // ϵͳ״̬ö
{
    SYSTEM_CHECK_OK = 0,       // ɹ
    SYSTEM_CHECK_ERROR = 1,    // ʧ
    SYSTEM_CHECK_NOT_FOUND = 2 // 豸δҵ
} system_check_status_t;

typedef struct // FlashϢṹ
{
    uint32_t flash_id;            // Flash ID
    char model_name[16];          // ͺ
    uint32_t capacity_mb;         // (MB)
    system_check_status_t status; // ״̬
} flash_info_t;

typedef struct // SDϢṹ
{
    uint32_t capacity_mb;         // (MB)
    uint32_t sector_count;        // 
    uint16_t sector_size;         // С
    system_check_status_t status; // ״̬
} sd_card_info_t;

typedef struct // ϵͳϢṹ
{
    flash_info_t flash_info;          // FlashϢ
    sd_card_info_t sd_info;           // SDϢ
    system_check_status_t rtc_status; // RTC״̬
} system_info_t;

// 
void perform_sys_diagnostics(void);                                        // ϵͳԼ : :
system_check_status_t check_storage_flash(flash_info_t *flash_info);  // Flash :FlashϢṹָ :״̬
system_check_status_t check_storage_tf(sd_card_info_t *sd_info); // SD :SDϢṹָ :״̬
void print_rtc_overview(void);                                           // ӡRTCʱ : :
void display_sys_info(const system_info_t *info);                   // ӡϵͳϢ :ϵͳϢṹָ :
const char *get_flash_model_name(uint32_t flash_id);                 // IDȡFlashͺ :Flash ID :ͺַ

#endif // __SYSTEM_CHECK_H__
