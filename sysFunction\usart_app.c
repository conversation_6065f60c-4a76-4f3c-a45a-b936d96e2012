// ļusart_app.c
// ܣͨӦģ飬ṩû
// ߣ״׵ӹ
// ȨCopyright (c) 2024 ״׵ӹ. All rights reserved.

#include "usart_app.h"
#include "stdlib.h" // ׼⺯
#include "stdarg.h" // ɱ
#include "string.h" // ַ
#include "stdio.h"  // ׼
#include "usart.h"  // 
#include "mydefine.h" // ȫֶ

uint16_t uart_rx_index = 0;                // ڽ
uint32_t uart_rx_ticks = 0;                // ڽʱ
uint8_t uart_rx_buffer[128] = {0};         // ڽջ
uint8_t uart_rx_dma_buffer[128] = {0};     // DMAջ
uint8_t uart_dma_buffer[128] = {0};        // DMA
uint8_t uart_flag = 0;                     // ڱ־λ
struct rt_ringbuffer uart_ringbuffer;      // λṹ
uint8_t ringbuffer_pool[128];              // λڴ

static cmd_state_t g_cmd_state = CMD_STATE_IDLE; // ״̬

uint8_t flag_output_active = 0; // ʹ
uint32_t last_output_timestamp = 0;       // ϴʱ

output_format_t g_output_format = OUTPUT_FORMAT_NORMAL; // ǰʽ

// 
static void convert_voltage_to_hex_format(float measured_voltage, uint16_t *integer_part, uint16_t *decimal_part);
static void test_unhide_conversion(const char* hex_data);
static void test_data_storage(void);

uint32_t convert_rtc_to_unix_timestamp(RTC_TimeTypeDef *time, RTC_DateTypeDef *date) // Unixʱת :ʱṹ,ڽṹ :Unixʱ
{
	static const uint8_t days_in_month[12] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31}; // ÿ()

	uint32_t year = date->Year + 2000; // תΪ4λ
	uint32_t month = date->Month;       // ·
	uint32_t day = date->Date;          // 
	uint32_t hour = time->Hours;        // Сʱ
	uint32_t minute = time->Minutes;    // 
	uint32_t second = time->Seconds;    // 

	uint32_t days = 0; // 1970굽ָݵ

	for (uint32_t y = 1970; y < year; y++) // ݹ׵
	{
		if ((y % 4 == 0 && y % 100 != 0) || (y % 400 == 0)) // ж
		{
			days += 366; // 
		}
		else
		{
			days += 365; // ƽ
		}
	}

	for (uint32_t m = 1; m < month; m++) // ·ݹ׵
	{
		days += days_in_month[m - 1];
		if (m == 2 && ((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0))) // Ѿ2£Ҫ1
		{
			days += 1;
		}
	}

	days += (day - 1); // ϵǰµ(1ΪǴ1ſʼ)

	// תΪ*24*3600 + Сʱ*3600 + *60 + 
	// ޸ʱ⣺ȥ8Сʱ(28800)תΪUTCʱ
	uint32_t timestamp = days * 86400 + hour * 3600 + minute * 60 + second - 28800;

	return timestamp;
}

// Unixʱתȷ
static void test_unix_timestamp_conversion(void)
{
	// 2000-01-02 8:00:21  946771221
	RTC_TimeTypeDef test_time = {0};
	RTC_DateTypeDef test_date = {0};

	test_time.Hours = 8;
	test_time.Minutes = 0;
	test_time.Seconds = 21;

	test_date.Year = 0;  // 20002λʾ
	test_date.Month = 1; // 1
	test_date.Date = 2;	 // 2

	uint32_t result = convert_rtc_to_unix_timestamp(&test_time, &test_date);

	// Խ֤
	my_printf(&huart1, "Test: 2000-01-02 8:00:21 -> %lu (expected: 946771221)\r\n", result);
	my_printf(&huart1, "Timestamp hex: %08X\r\n", result);

	// hideģʽ
	char hex_output[32];
	format_hex_output(result, 3.2911376953125f, 0, hex_output);
	my_printf(&huart1, "Hide format test: %s (expected: 386E951500034A88)\r\n", hex_output);

	// ֤ѹת
	uint16_t int_part, dec_part;
	convert_voltage_to_hex_format(3.2911376953125f, &int_part, &dec_part);
	my_printf(&huart1, "Voltage 3.291V -> %04X%04X (expected: 00034A88)\r\n", int_part, dec_part);

	// Խܹ
	test_unhide_conversion("386E951500034A88");
}

// hideݵĲԺ
static void test_unhide_conversion(const char* hex_data)
{
	if (strlen(hex_data) < 16) {
		my_printf(&huart1, "Invalid hex data length\r\n");
		return;
	}

	// ʱǰ8λ
	uint32_t timestamp;
	sscanf(hex_data, "%8X", &timestamp);

	// ѹ8λ
	uint16_t voltage_int, voltage_dec;
	sscanf(hex_data + 8, "%4X%4X", &voltage_int, &voltage_dec);

	// תʱΪʱ䣨8Сʱʱƫƣ
	uint32_t local_timestamp = timestamp + 28800; // 8СʱתΪʱ
	uint32_t days = local_timestamp / 86400;
	uint32_t remaining = local_timestamp % 86400;
	uint32_t hours = remaining / 3600;
	uint32_t minutes = (remaining % 3600) / 60;
	uint32_t seconds = remaining % 60;

	// գ򻯰汾1970꿪ʼ
	uint32_t year = 1970;
	while (days >= 365) {
		if ((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0)) {
			if (days >= 366) {
				days -= 366;
				year++;
			} else {
				break;
			}
		} else {
			days -= 365;
			year++;
		}
	}

	// ռ
	uint32_t month = 1;
	uint32_t day = days + 1;

	// תѹ
	float measured_voltage = (float)voltage_int + (float)voltage_dec / 65536.0f;

	my_printf(&huart1, "Unhide test: %s\r\n", hex_data);
	my_printf(&huart1, "Timestamp: %lu\r\n", timestamp);
	my_printf(&huart1, "(%04lu-%02lu-%02lu %02lu:%02lu:%02lu)\r\n", year, month, day, hours, minutes, seconds);
	my_printf(&huart1, "Voltage: %.6fV\r\n", measured_voltage);
}

// ݴ洢
static void test_data_storage(void)
{
	my_printf(&huart1, "Testing data storage...\r\n");

	// sampleݴ洢
	my_printf(&huart1, "Testing sample storage...\r\n");
	data_storage_status_t result = data_storage_write_sample(3.3f);
	my_printf(&huart1, "Sample storage result: %d\r\n", result);

	// overlimitݴ洢
	my_printf(&huart1, "Testing overlimit storage...\r\n");
	result = data_storage_write_overlimit(5.0f, 4.5f);
	my_printf(&huart1, "Overlimit storage result: %d\r\n", result);

	// hidedataݴ洢
	my_printf(&huart1, "Testing hidedata storage...\r\n");
	result = data_storage_write_hidedata(3.8f, 1);
	my_printf(&huart1, "Hidedata storage result: %d\r\n", result);

	my_printf(&huart1, "Data storage test completed.\r\n");
}

// ѹתHEXʽ
static void convert_voltage_to_hex_format(float measured_voltage, uint16_t *integer_part, uint16_t *decimal_part)
{
	// ȡѹ
	*integer_part = (uint16_t)measured_voltage;

	// С֣(measured_voltage - ) * 65536
	float fractional = measured_voltage - (float)(*integer_part);
	*decimal_part = (uint16_t)(fractional * 65536.0f);
}

// ԵѹHEX뺯ȷ
static void test_voltage_hex_encoding(void)
{
	// 12.5V  000C8000
	float test_voltage = 12.5f;
	uint16_t int_part, dec_part;

	convert_voltage_to_hex_format(test_voltage, &int_part, &dec_part);

	// Խ֤
	my_printf(&huart1, "Test: %.1fV -> %04X%04X (expected: 000C8000)\r\n",
			  test_voltage, int_part, dec_part);
}

// HEXʽ
void format_hex_output(uint32_t timestamp, float measured_voltage, uint8_t is_overlimit, char *output)
{
	uint16_t int_part, dec_part;

	// ѹתΪHEXʽ
	convert_voltage_to_hex_format(measured_voltage, &int_part, &dec_part);

	// ʽ8λHEXʱ+8λHEXѹ+ޱ־*
	sprintf(output, "%08X%04X%04X%s",
			timestamp,
			int_part,
			dec_part,
			is_overlimit ? "*" : "");
}

// HEXʽȷ
static void test_hex_format_output(void)
{
	// 2025-01-01 12:30:45, 12.5V, δ
	uint32_t test_timestamp = 1735705845; // Ӧ2025-01-01 12:30:45
	float test_voltage = 12.5f;
	uint8_t test_overlimit = 0;
	char output_buffer[32];

	format_hex_output(test_timestamp, test_voltage, test_overlimit, output_buffer);

	// Խ֤
	my_printf(&huart1, "Test HEX output: %s (expected: 6774C4F5000C8000)\r\n", output_buffer);

	// Գ
	test_overlimit = 1;
	format_hex_output(test_timestamp, test_voltage, test_overlimit, output_buffer);
	my_printf(&huart1, "Test HEX output (overlimit): %s (expected: 6774C4F5000C8000*)\r\n", output_buffer);
}

int my_printf(UART_HandleTypeDef *huart, const char *format, ...) // ʽ :ھ,ʽַ,ɱ :ַ
{
	char buffer[512]; // 
	va_list arg;      // ɱб
	int len;          // 
	va_start(arg, format);
	len = vsnprintf(buffer, sizeof(buffer), format, arg); // ʽַ
	va_end(arg);
	HAL_UART_Transmit(huart, (uint8_t *)buffer, (uint16_t)len, 0xFF); // ڷ
	return len;
}

void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart) // ڽɻص :ھ :
{
	if (huart->Instance == USART1) // ǷΪUSART1
	{
		uart_rx_ticks = uwTick;                                                 // ¼ʱ
		uart_rx_index++;                                                        // ӽ
		HAL_UART_Receive_IT(&huart1, &uart_rx_buffer[uart_rx_index], 1);      // һֽ
	}
}

void HAL_UARTEx_RxEventCallback(UART_HandleTypeDef *huart, uint16_t Size) // DMA¼ص :ھ,մС :
{
	if (huart->Instance == USART1) // ǷΪUSART1
	{
		HAL_UART_DMAStop(huart); // ֹͣDMA

		rt_ringbuffer_put(&uart_ringbuffer, uart_rx_dma_buffer, Size); // ݷ뻷λ

		memset(uart_rx_dma_buffer, 0, sizeof(uart_rx_dma_buffer)); // DMA

		HAL_UARTEx_ReceiveToIdle_DMA(&huart1, uart_rx_dma_buffer, sizeof(uart_rx_dma_buffer)); // DMA

		__HAL_DMA_DISABLE_IT(&hdma_usart1_rx, DMA_IT_HT); // ð봫ж
	}
}

void uart_cmd_handler(uint8_t *buffer, uint16_t length)
{
	// ϵͳԼ
	if (strcmp((char *)buffer, "test") == 0)
	{
		perform_sys_diagnostics(); // ִϵͳԼ
	}
	// ʱ
	else if (strcmp((char *)buffer, "testtime") == 0)
	{
		test_unix_timestamp_conversion(); // ʱת
	}
	// ܲ
	else if (strncmp((char *)buffer, "testhide ", 9) == 0)
	{
		if (length > 9) {
			char *hex_data = (char *)buffer + 9; // "testhide "
			test_unhide_conversion(hex_data); // Խ
		} else {
			my_printf(&huart1, "Usage: testhide <hex_data>\r\n");
		}
	}
	// ݴ洢
	else if (strcmp((char *)buffer, "teststorage") == 0)
	{
		test_data_storage(); // ݴ洢
	}
	// RTCʱ
	else if (strcmp((char *)buffer, "RTC Config") == 0)
	{
		process_rtc_config_cmd(); // RTC Config߼
	}
	// RTCʱѯ
	else if (strcmp((char *)buffer, "RTC now") == 0)
	{
		rtc_show_time_serial(); // ӡǰRTCʱ
	}
	// ļȡ
	else if (strcmp((char *)buffer, "conf") == 0)
	{
		process_conf_cmd(); // conf߼
	}
	// 
	else if (strcmp((char *)buffer, "ratio") == 0)
	{
		process_ratio_cmd(); // ratio߼
	}
	// ֵ
	else if (strcmp((char *)buffer, "limit") == 0)
	{
		process_limit_cmd(); // limit߼
	}
	// ñ
	else if (strcmp((char *)buffer, "configsave") == 0)
	{
		process_configsave_cmd(); // configsave߼
	}
	// öȡ
	else if (strcmp((char *)buffer, "configread") == 0)
	{
		process_configread_cmd(); // configread߼
	}
	// 
	else if (strcmp((char *)buffer, "start") == 0)
	{
		process_start_cmd(); // start߼
	}
	// ֹͣ
	else if (strcmp((char *)buffer, "stop") == 0)
	{
		process_stop_cmd(); // stop߼
	}
	// ģʽ
	else if (strcmp((char *)buffer, "hide") == 0)
	{
		process_hide_cmd(); // hide߼
	}
	// ȡ
	else if (strcmp((char *)buffer, "unhide") == 0)
	{
		process_unhide_cmd(); // unhide߼
	}
	// ʽ봦
	else if (g_cmd_state != CMD_STATE_IDLE)
	{
		handle_interactive_input((char *)buffer); // ʽ
	}
}

void uart_task(void) //  : :
{
	uint16_t length; // ݳ

	length = rt_ringbuffer_data_len(&uart_ringbuffer); // ȡλݳ

	if (length > 0) // Ҫ
	{
		rt_ringbuffer_get(&uart_ringbuffer, uart_dma_buffer, length); // ӻλȡ

		uart_cmd_handler(uart_dma_buffer, length); // յ

		memset(uart_dma_buffer, 0, sizeof(uart_dma_buffer)); // ջ
	}

	handle_sampling_output(); // 
}

// conf - TFȡconfig.iniļò
void process_conf_cmd(void)
{
	ini_config_t ini_config;
	config_params_t config_params;

	// ¼ִе־
	data_storage_write_log("conf command");

	// config.iniļ
	ini_status_t ini_status = ini_parse_file("config.ini", &ini_config);

	if (ini_status == INI_FILE_NOT_FOUND)
	{
		my_printf(&huart1, "config.ini file not found.\r\n");
		return;
	}

	if (ini_status != INI_OK)
	{
		my_printf(&huart1, "config.ini format error.\r\n");
		return;
	}

	// Ƿ
	if (!ini_config.ratio_found || !ini_config.limit_found)
	{
		my_printf(&huart1, "config.ini missing parameters.\r\n");
		return;
	}

	// ֤Χ
	if (config_validate_ratio(ini_config.ratio) != CONFIG_OK)
	{
		my_printf(&huart1, "ratio parameter out of range (0-100).\r\n");
		return;
	}

	if (config_validate_limit(ini_config.limit) != CONFIG_OK)
	{
		my_printf(&huart1, "limit parameter out of range (0-500).\r\n");
		return;
	}

	// ȡǰ
	if (config_get_params(&config_params) != CONFIG_OK)
	{
		my_printf(&huart1, "config system error.\r\n");
		return;
	}

	// ò
	config_params.ratio = ini_config.ratio;
	config_params.limit = ini_config.limit;

	// 
	if (config_set_params(&config_params) != CONFIG_OK)
	{
		my_printf(&huart1, "config update failed.\r\n");
		return;
	}

	// 浽Flash
	if (config_save_to_flash() != CONFIG_OK)
	{
		my_printf(&huart1, "config save to flash failed.\r\n");
		return;
	}

	// ý
	my_printf(&huart1, "Ratio = %.1f\r\n", ini_config.ratio);
	my_printf(&huart1, "Limit = %.1f\r\n", ini_config.limit);
	my_printf(&huart1, "config read success\r\n");

	// ¼ɹ־
	char log_msg[128];
	sprintf(log_msg, "config read success - ratio %.1f, limit %.1f", ini_config.ratio, ini_config.limit);
	data_storage_write_log(log_msg);
}

// ratio - ʾǰȴû
void process_ratio_cmd(void)
{
	config_params_t config_params;

	// ¼ratio־
	data_storage_write_log("ratio command");

	// ȡǰ
	if (config_get_params(&config_params) != CONFIG_OK)
	{
		my_printf(&huart1, "config system error.\r\n");
		return;
	}

	// ʾǰratioֵ
	my_printf(&huart1, "Ratio=%.1f\r\n", config_params.ratio);

	// ʾû
	my_printf(&huart1, "Input value(0~100):\r\n");

	// ״̬ȴ
	g_cmd_state = CMD_STATE_WAIT_RATIO;
}

// ʽ
void handle_interactive_input(char *input)
{
	float value;
	config_params_t config_params;

	// ֵ
	if (sscanf(input, "%f", &value) != 1)
	{
		my_printf(&huart1, "invalid input format.\r\n");
		g_cmd_state = CMD_STATE_IDLE;
		return;
	}

	// ȡǰ
	if (config_get_params(&config_params) != CONFIG_OK)
	{
		my_printf(&huart1, "config system error.\r\n");
		g_cmd_state = CMD_STATE_IDLE;
		return;
	}

	if (g_cmd_state == CMD_STATE_WAIT_RATIO)
	{
		// ֤ratioΧ
		if (config_validate_ratio(value) != CONFIG_OK)
		{
			my_printf(&huart1, "ratio invalid\r\n");
			my_printf(&huart1, "Ratio = %.1f\r\n", config_params.ratio);
		}
		else
		{
			// ratio
			config_params.ratio = value;
			if (config_set_params(&config_params) == CONFIG_OK)
			{
				my_printf(&huart1, "ratio modified success\r\n");
				my_printf(&huart1, "Ratio = %.1f\r\n", value);

				// ¼ratioóɹ־
				char log_msg[64];
				sprintf(log_msg, "ratio config success to %.1f", value);
				data_storage_write_log(log_msg);
			}
			else
			{
				my_printf(&huart1, "config update failed.\r\n");
			}
		}
		g_cmd_state = CMD_STATE_IDLE;
	}
	else if (g_cmd_state == CMD_STATE_WAIT_LIMIT)
	{
		// ֤limitΧ
		if (config_validate_limit(value) != CONFIG_OK)
		{
			my_printf(&huart1, "limit invalid\r\n");
			my_printf(&huart1, "limit = %.1f\r\n", config_params.limit);
		}
		else
		{
			// limit
			config_params.limit = value;
			if (config_set_params(&config_params) == CONFIG_OK)
			{
				my_printf(&huart1, "limit modified success\r\n");
				my_printf(&huart1, "limit = %.1f\r\n", value);

				// ¼limitóɹ־
				char log_msg[64];
				sprintf(log_msg, "limit config success to %.1f", value);
				data_storage_write_log(log_msg);
			}
			else
			{
				my_printf(&huart1, "config update failed.\r\n");
			}
		}
		g_cmd_state = CMD_STATE_IDLE;
	}
	else if (g_cmd_state == CMD_STATE_WAIT_RTC)
	{
		// RTCʱ
		HAL_StatusTypeDef status = rtc_set_time_from_string(input);
		if (status == HAL_OK)
		{
			my_printf(&huart1, "RTC Config success\r\n");
			my_printf(&huart1, "Time: %s\r\n", input);

			// ¼RTCóɹ־
			char log_msg[128];
			sprintf(log_msg, "RTC config success to %s", input);
			data_storage_write_log(log_msg);
		}
		else
		{
			my_printf(&huart1, "RTC Config failed\r\n");
			my_printf(&huart1, "Invalid time format. Please use: 2025-01-01 15:00:10 or 2025010112:00:30\r\n");
		}
		g_cmd_state = CMD_STATE_IDLE;
	}
}

// limit - ʾǰֵȴû
void process_limit_cmd(void)
{
	config_params_t config_params;

	// ¼limit־
	data_storage_write_log("limit command");

	// ȡǰ
	if (config_get_params(&config_params) != CONFIG_OK)
	{
		my_printf(&huart1, "config system error.\r\n");
		return;
	}

	// ʾǰlimitֵ
	my_printf(&huart1, "limit=%.1f\r\n", config_params.limit);

	// ʾû
	my_printf(&huart1, "Input value(0~500):\r\n");

	// ״̬ȴ
	g_cmd_state = CMD_STATE_WAIT_LIMIT;
}

// configsave - 浱ǰõFlash
void process_configsave_cmd(void)
{
	config_params_t config_params;

	// ȡǰò
	if (config_get_params(&config_params) != CONFIG_OK)
	{
		my_printf(&huart1, "config system error.\r\n");
		return;
	}

	// ʾǰ
	my_printf(&huart1, "ratio: %.2f\r\n", config_params.ratio);
	my_printf(&huart1, "limit: %.2f\r\n", config_params.limit);

	// õFlash
	if (config_save_to_flash() != CONFIG_OK)
	{
		my_printf(&huart1, "save parameters to flash failed.\r\n");
		return;
	}

	// ɹϢ
	my_printf(&huart1, "save parameters to flash\r\n");
}

// configread - Flashȡ
void process_configread_cmd(void)
{
	config_params_t config_params;

	// Flashȡ
	config_status_t status = config_load_from_flash();
	if (status != CONFIG_OK)
	{
		my_printf(&huart1, "read parameters from flash failed.\r\n");
		return;
	}

	// ɹϢ
	my_printf(&huart1, "read parameters from flash\r\n");

	// ȡȡòʾ
	if (config_get_params(&config_params) != CONFIG_OK)
	{
		my_printf(&huart1, "config system error.\r\n");
		return;
	}

	// ʾȡ
	my_printf(&huart1, "ratio: %.2f\r\n", config_params.ratio);
	my_printf(&huart1, "limit: %.2f\r\n", config_params.limit);
}

// start - 
void process_start_cmd(void)
{
	// ȷϵͳѳʼ
	sampling_init();

	// ϵͳ
	if (sampling_start() != SAMPLING_OK)
	{
		my_printf(&huart1, "sampling start failed.\r\n");
		return;
	}

	// Ϣ
	my_printf(&huart1, "Periodic Sampling\r\n");

	// ʾǰ
	sampling_cycle_t cycle = sampling_get_cycle();
	my_printf(&huart1, "sample cycle: %ds\r\n", (int)cycle);

	// ò
	flag_output_active = 1;
	last_output_timestamp = HAL_GetTick();

	// ¼־
	char log_msg[64];
	sprintf(log_msg, "sample start - cycle %ds (command)", (int)cycle);
	data_storage_write_log(log_msg);
}

// stop - ֹͣ
void process_stop_cmd(void)
{
	// ȷϵͳѳʼ
	sampling_init();

	// ֹͣϵͳ
	if (sampling_stop() != SAMPLING_OK)
	{
		my_printf(&huart1, "sampling stop failed.\r\n");
		return;
	}

	// ֹͣϢ
	my_printf(&huart1, "PeriodicSamplingSTOP\r\n");

	// ò
	flag_output_active = 0;

	// ¼ֹͣ־
	data_storage_write_log("sample stop (command)");
}

// 
void handle_sampling_output(void)
{
	// Ƿʹ
	if (!flag_output_active)
	{
		return;
	}

	// ״̬
	if (sampling_get_state() != SAMPLING_ACTIVE)
	{
		return;
	}

	// Ƿ񵽴ʱ
	uint32_t current_time = HAL_GetTick();
	sampling_cycle_t cycle = sampling_get_cycle();
	uint32_t cycle_ms = cycle * 1000; // תΪ

	if (current_time - last_output_timestamp >= cycle_ms)
	{
		// ʱ
		last_output_timestamp = current_time;

		// ȡǰʱ
		RTC_TimeTypeDef current_rtc_time = {0};
		RTC_DateTypeDef current_rtc_date = {0};
		HAL_RTC_GetTime(&hrtc, &current_rtc_time, RTC_FORMAT_BIN);
		HAL_RTC_GetDate(&hrtc, &current_rtc_date, RTC_FORMAT_BIN);

		// ȡǰѹ
		float measured_voltage = sampling_get_voltage();

		// Ƿ
		uint8_t is_overlimit = sampling_check_overlimit();

		// ʽвͬĴ
		if (g_output_format == OUTPUT_FORMAT_HIDDEN)
		{
			// ģʽHEXʽ
			uint32_t timestamp = convert_rtc_to_unix_timestamp(&current_rtc_time, &current_rtc_date);
			char hex_output[32];

			format_hex_output(timestamp, measured_voltage, is_overlimit, hex_output);
			my_printf(&huart1, "%s\r\n", hex_output);
		}
		else
		{
			// ģʽɶʽϢ
			if (is_overlimit)
			{
				// ȡlimitֵ
				config_params_t config_params;
				if (config_get_params(&config_params) == CONFIG_OK)
				{
					my_printf(&huart1, "%04d-%02d-%02d %02d:%02d:%02d ch0=%.2fV OverLimit(%.2f)!!\r\n",
							  current_rtc_date.Year + 2000,
							  current_rtc_date.Month,
							  current_rtc_date.Date,
							  current_rtc_time.Hours,
							  current_rtc_time.Minutes,
							  current_rtc_time.Seconds,
							  measured_voltage,
							  config_params.limit);
				}
				else
				{
					my_printf(&huart1, "%04d-%02d-%02d %02d:%02d:%02d ch0=%.2fV OverLimit!!\r\n",
							  current_rtc_date.Year + 2000,
							  current_rtc_date.Month,
							  current_rtc_date.Date,
							  current_rtc_time.Hours,
							  current_rtc_time.Minutes,
							  current_rtc_time.Seconds,
							  measured_voltage);
				}
			}
			else
			{
				my_printf(&huart1, "%04d-%02d-%02d %02d:%02d:%02d ch0=%.2fV\r\n",
						  current_rtc_date.Year + 2000,
						  current_rtc_date.Month,
						  current_rtc_date.Date,
						  current_rtc_time.Hours,
						  current_rtc_time.Minutes,
						  current_rtc_time.Seconds,
						  measured_voltage);
			}
		}

		// ݴ洢ʽѡ洢ʽ
		// ע⣺ݴ洢ʽ޹أҪ洢
		if (g_output_format == OUTPUT_FORMAT_HIDDEN)
		{
			// ģʽ洢hideDataļУ洢sample
			data_storage_status_t result = data_storage_write_hidedata(measured_voltage, is_overlimit);
			if (result != DATA_STORAGE_OK)
			{
				// 洢ʧʱϢ
				// my_printf(&huart1, "Warning: hideData storage failed\r\n");
			}
		}
		else
		{
			// ģʽ洢sample
			data_storage_status_t result = data_storage_write_sample(measured_voltage);
			if (result != DATA_STORAGE_OK)
			{
				// 洢ʧʱϢ
				// my_printf(&huart1, "Warning: sample storage failed\r\n");
			}
		}

		// ݶ洢overLimitļУhideģʽ
		if (is_overlimit)
		{
			config_params_t config_params;
			float limit_value = 0.0f;

			// ȡlimitֵڴ洢
			if (config_get_params(&config_params) == CONFIG_OK)
			{
				limit_value = config_params.limit;
			}

			data_storage_status_t result = data_storage_write_overlimit(measured_voltage, limit_value);
			if (result != DATA_STORAGE_OK)
			{
				// 洢ʧʱϢ
				// my_printf(&huart1, "Warning: overLimit storage failed\r\n");
			}
		}
	}
}

// hide - лģʽ
void process_hide_cmd(void)
{
	// Ϊʽ
	g_output_format = OUTPUT_FORMAT_HIDDEN;

	// ¼hideģʽл־
	data_storage_write_log("hide data");

	// ȷϢģʽľĬ
	// my_printf(&huart1, "Output format switched to hidden.\r\n");
}

// unhide - лģʽ
void process_unhide_cmd(void)
{
	// Ϊʽ
	g_output_format = OUTPUT_FORMAT_NORMAL;

	// ¼unhideģʽл־
	data_storage_write_log("unhide data");

	// ȷϢ
	// my_printf(&huart1, "Output format switched to normal.\r\n");
}

// RTC Config - ʾûʱ䲢ȴ
void process_rtc_config_cmd(void)
{
	// ¼RTC Config־
	data_storage_write_log("RTC Config command");

	// ʾûʱ
	my_printf(&huart1, "Input Datetime\r\n");

	// ״̬ȴRTCʱ
	g_cmd_state = CMD_STATE_WAIT_RTC;
}
