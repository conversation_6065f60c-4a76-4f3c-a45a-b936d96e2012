// ļusart_app.h
// ܣͨӦͷļṩûӿ
// ߣ״׵ӹ
// ȨCopyright (c) 2024 ״׵ӹ. All rights reserved.

#ifndef __USART_APP_H__
#define __USART_APP_H__

#include "mydefine.h"     // ȫֶͷļ
#include "data_storage.h" // ݴ洢ģ

int my_printf(UART_HandleTypeDef *huart, const char *format, ...);        // ʽ :ھ,ʽַ,ɱ :ַ
void uart_task(void);                                                     //  : :
void uart_cmd_handler(uint8_t *buffer, uint16_t length);                //  :, :

typedef enum // ״̬ö
{
    CMD_STATE_IDLE = 0,       // ״̬
    CMD_STATE_WAIT_RATIO = 1, // ȴratio
    CMD_STATE_WAIT_LIMIT = 2, // ȴlimit
    CMD_STATE_WAIT_RTC = 3    // ȴRTCʱ
} cmd_state_t;

typedef enum // ʽö
{
    OUTPUT_FORMAT_NORMAL = 0, // ʽ
    OUTPUT_FORMAT_HIDDEN = 1  // ʽ
} output_format_t;

// 
void process_conf_cmd(void);             // conf : :
void process_ratio_cmd(void);            // ratio : :
void process_limit_cmd(void);            // limit : :
void process_configsave_cmd(void);       // configsave : :
void process_configread_cmd(void);       // configread : :
void process_start_cmd(void);            // start(/): :
void process_stop_cmd(void);             // stop(/): :
void process_hide_cmd(void);             // hide : :
void process_unhide_cmd(void);           // unhide : :
void process_rtc_config_cmd(void);       // RTC Config : :
void handle_sampling_output(void);          // ʾ : :
void handle_interactive_input(char *input); // ʽ :ַ :

// ߺ(ģʹ)
uint32_t convert_rtc_to_unix_timestamp(RTC_TimeTypeDef *time, RTC_DateTypeDef *date);          // Unixʱת :ʱṹ,ڽṹ :Unixʱ
void format_hex_output(uint32_t timestamp, float measured_voltage, uint8_t is_overlimit, char *output); // HEXʽ :ʱ,ѹֵ,ޱ־, :

// ȫֱ(ģʹ)
extern uint8_t flag_output_active;  // ʹܱ־
extern uint32_t last_output_timestamp;        // ϴʱ
extern output_format_t g_output_format;    // ǰʽ

#endif
