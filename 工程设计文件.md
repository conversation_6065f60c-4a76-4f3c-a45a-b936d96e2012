# 基于sysFunction的嵌入式数据采集系统设计文档

## 第一章 工程任务分析

### 1.1 系统功能概述

这个项目是一个基于GD32F470单片机的数据采集系统。通过分析sysFunction代码，我们可以看到系统主要完成以下工作：

#### 1.1.1 主要功能
- **数据采集**：每隔5秒、10秒或15秒采集一次电压值
- **实时显示**：OLED屏幕显示当前时间和电压
- **数据存储**：把采集的数据保存到SD卡
- **状态指示**：LED灯显示系统工作状态
- **用户操作**：通过按键控制系统
- **串口通信**：通过电脑发送命令控制系统

从代码中可以看到设备ID定义：
```c
#define DEVICE_ID "Device_ID:2025-CIMC-2025129954"
```

### 1.2 技术要求分析

#### 1.2.1 性能指标
- **测量精度**：12位ADC，可以测量0-3.3V的电压
- **时间精度**：使用RTC时钟，支持Unix时间戳
- **存储方式**：Flash + SD卡双重保存
- **响应速度**：按键响应时间小于10ms
- **工作稳定性**：支持长时间连续运行

#### 1.2.2 设计目标
1. **稳定可靠**：系统能自动检测故障并恢复
2. **模块化**：各个功能独立，方便修改和维护
3. **省电**：优化程序，降低功耗
4. **易用**：操作简单，界面清晰
5. **数据安全**：多重备份，防止数据丢失

#### 1.2.3 限制条件
- **硬件限制**：受GD32F470性能限制
- **存储限制**：Flash和RAM容量有限
- **时间限制**：关键操作必须及时完成
- **功耗限制**：适合电池供电使用
- **成本限制**：使用常见器件，控制成本

## 第二章 系统单元功能分析设计

### 2.1 核心功能模块设计

#### 2.1.1 任务调度模块
这个模块负责管理系统中各个功能的执行时间，就像一个时间管理员。

从sysFunction代码中可以看到任务结构定义：
```c
typedef struct {
    void (*task_func)(void);  // 要执行的函数
    uint32_t rate_ms;         // 多久执行一次(毫秒)
    uint32_t last_run;        // 上次执行的时间
} task_t;
```

**各个任务的执行周期**：
- LED任务：1ms执行一次，控制指示灯
- ADC任务：100ms执行一次，读取电压值
- 按键任务：5ms执行一次，检测按键
- 串口任务：5ms执行一次，处理通信
- OLED任务：1ms执行一次，更新显示
- 采样任务：10ms执行一次，控制采样流程

**工作原理**：
系统通过HAL_GetTick()获取当前时间，检查每个任务是否到了该执行的时间，如果到了就执行对应的函数。

#### 2.1.2 数据采集模块
这个模块负责采集电压数据，是整个系统的核心。

**采样状态管理**：
```c
typedef enum {
    SAMPLING_IDLE,    // 空闲状态，没有在采样
    SAMPLING_ACTIVE   // 工作状态，正在采样
} sampling_state_t;
```

**采样周期设置**：
```c
typedef enum {
    CYCLE_5S = 5000,   // 5秒采样一次
    CYCLE_10S = 10000, // 10秒采样一次
    CYCLE_15S = 15000  // 15秒采样一次
} sampling_cycle_t;
```

**电压计算**：
ADC读取的是数字值（0-4095），需要转换成实际电压：
```c
float voltage = (float)adc_value * 3.3f / 4096.0f;
```

**LED状态指示**：
- LED1闪烁：正在采样
- LED2常亮：电压超过设定值

#### 2.1.3 数据存储模块
这个模块负责把采集的数据保存到SD卡。

**存储数据类型**：
```c
typedef enum {
    STORAGE_SAMPLE,    // 普通采样数据
    STORAGE_OVERLIMIT, // 超限数据
    STORAGE_LOG,       // 系统日志
    STORAGE_HIDEDATA   // 隐藏数据
} storage_type_t;
```

**文件命名规则**：
文件名格式：`类型_日期_启动次数_文件序号.txt`
例如：`sample_20250101_001_001.txt`

**数据格式**：
- 普通数据：`时间戳 电压值`
- 隐藏数据：十六进制编码格式

### 2.2 辅助功能模块设计

#### 2.2.1 系统检测模块
这个模块负责检查硬件是否正常工作。

**Flash存储检测**：
检查内部Flash存储器是否正常：
```c
// 检测Flash ID
uint32_t flash_id = gd25qxx_read_id();
if (flash_id == 0x00000000 || flash_id == 0xFFFFFFFF) {
    // Flash检测失败
    return SYSTEM_CHECK_ERROR;
}
```

**SD卡检测**：
检查SD卡是否插入并能正常使用：
```c
// 初始化SD卡
DSTATUS status = disk_initialize(0);
if (status != 0) {
    // SD卡初始化失败
    return SYSTEM_CHECK_ERROR;
}
```

**RTC时钟检测**：
检查实时时钟是否正常工作，提供时间管理功能。

#### 2.2.2 配置管理模块
这个模块负责保存和读取系统设置参数。

**配置参数结构**：
```c
typedef struct {
    uint32_t magic;              // 标识符，用于验证数据有效性
    uint8_t version;             // 版本号
    float ratio;                 // 比例系数
    float limit;                 // 超限阈值
    sampling_cycle_t cycle;      // 采样周期
    uint32_t crc32;              // 校验码，确保数据完整
} config_params_t;
```

**参数保存位置**：
- Flash地址：0x1F0000（配置参数区）
- Flash地址：0x0000（设备ID区）

**设备ID格式**：
```c
#define DEVICE_ID "Device_ID:2025-CIMC-2025129954"
```

#### 2.2.3 人机交互模块
这个模块负责用户操作和信息显示。

**按键功能定义**：
```c
// 按键功能映射
KEY1: 启动/停止采样
KEY2: 停止采样
KEY3: 设置10秒采样周期
KEY4: 设置15秒采样周期
```

**OLED显示内容**：
- 空闲时显示："systemidle"
- 采样时显示：时间和电压值
- 格式：`HH:MM:SS XX.XX V`

**串口命令**：
主要命令包括：
- `test` - 系统自检
- `start` - 开始采样
- `stop` - 停止采样
- `teststorage` - 测试存储功能
- `RTC Config` - 设置时间

## 第三章 综合系统设计

### 3.1 系统架构设计

#### 3.1.1 分层架构
系统采用分层设计，每一层负责不同的功能：

**1. 硬件层**：
- GPIO：控制LED、按键、片选信号
- ADC：电压采样
- RTC：时间管理
- UART：串口通信
- I2C：OLED显示
- SPI：Flash存储

**2. 驱动层**：
- u8g2库：OLED显示驱动
- gd25qxx：Flash存储驱动
- FATFS：SD卡文件系统
- ebtn：按键处理库

**3. 应用层**：
从代码中可以看到主要的应用模块：
```c
// 主要应用模块
sampling_control_init();    // 采样控制
data_storage_init();        // 数据存储
config_manager_init();      // 配置管理
system_check_init();        // 系统检测
```

**4. 任务层**：
任务调度器管理所有功能的执行时间

**5. 用户接口层**：
- 按键操作
- OLED显示
- 串口命令

#### 3.1.2 模块间通信方式
各个模块之间通过以下方式进行通信：

**全局变量共享**：
```c
// 系统状态全局变量
extern sampling_state_t g_sampling_state;
extern float g_current_voltage;
extern config_params_t g_config;
```

**函数调用**：
每个模块提供标准的接口函数：
```c
// 采样控制接口
sampling_result_t sampling_start(void);
sampling_result_t sampling_stop(void);
sampling_state_t sampling_get_state(void);

// 数据存储接口
storage_result_t data_storage_write_sample(float voltage);
storage_result_t data_storage_write_log(const char* message);
```

**事件驱动**：
- 按键按下触发相应处理函数
- 串口接收到命令触发解析函数
- 定时器中断触发任务调度

### 3.2 系统启动流程

#### 3.2.1 初始化顺序
系统启动时按以下顺序初始化各个模块：

```c
int main(void) {
    // 1. 硬件初始化
    HAL_Init();
    SystemClock_Config();
    GPIO_Init();
    ADC_Init();
    RTC_Init();
    UART_Init();

    // 2. 存储系统初始化
    gd25qxx_init();           // Flash初始化
    f_mount(&fs, "", 1);      // SD卡挂载

    // 3. 加载配置参数
    device_id_init();         // 设备ID
    config_load_from_flash(); // 配置参数

    // 4. 应用模块初始化
    sampling_control_init();
    data_storage_init();
    oled_init();

    // 5. 启动任务调度器
    scheduler_start();

    // 6. 进入主循环
    while(1) {
        scheduler_run();
    }
}
```

#### 3.2.2 运行时管理
系统运行时的管理机制：

**系统监控**：
定期执行系统自检：
```c
void perform_sys_diagnostics(void) {
    check_storage_flash();    // 检查Flash
    check_storage_tf();       // 检查SD卡
    check_rtc_time();         // 检查RTC
}
```

**错误处理**：
- 检测到错误时记录日志
- 自动重试失败的操作
- 严重错误时系统重启

### 3.3 数据流设计

#### 3.3.1 数据流向
系统的数据流向如下：

```
ADC采样 → 采样控制 → 数据存储 → SD卡文件
            ↓
        OLED显示 ← 系统状态
            ↓
        LED指示 ← 状态信息
```

**数据采集流程**：
1. ADC读取电压值
2. 采样控制模块处理数据
3. 数据存储模块保存到SD卡
4. 更新系统状态

**状态反馈流程**：
1. 收集各模块状态
2. 格式化显示信息
3. 更新OLED显示
4. 控制LED指示灯

#### 3.3.2 数据同步机制
各模块间的数据同步：

**实时数据**：
采样数据在各模块间实时共享：
```c
extern float g_current_voltage;    // 当前电压值
extern uint32_t g_sample_count;   // 采样计数
extern sampling_state_t g_state;  // 采样状态
```

**缓存数据**：
- 串口接收：128字节环形缓冲区
- 显示缓存：双缓冲机制
- 存储缓冲：512字节临时缓冲区

### 3.4 时序协调设计

#### 3.4.1 任务执行周期
系统中各任务的执行周期安排：

**高频任务（1ms）**：
- LED状态更新：实时响应状态变化
- OLED显示刷新：保证显示流畅

**中频任务（5-10ms）**：
- 按键扫描：及时响应用户操作
- 串口处理：保证通信实时性
- 采样控制：管理采样时序

**低频任务（100ms）**：
- ADC采样：定期获取电压值
- 系统监控：检查系统健康状态

#### 3.4.2 资源冲突解决
避免各模块间的资源冲突：

**优先级管理**：
- 采样、存储：最高优先级
- 按键、显示：中等优先级
- 监控、检测：较低优先级

**资源访问协调**：
- Flash访问：配置和存储模块协调使用
- SD卡访问：数据存储和检测模块协调
- 串口资源：通信和调试功能协调

### 3.5 可靠性设计

#### 3.5.1 故障检测
系统的故障检测机制：

**硬件故障检测**：
```c
// Flash检测
if (gd25qxx_read_id() == 0xFFFFFFFF) {
    // Flash故障处理
}

// SD卡检测
if (f_mount(&fs, "", 1) != FR_OK) {
    // SD卡故障处理
}
```

**软件故障检测**：
- 任务执行时间监控
- 内存使用检查
- 数据完整性校验

#### 3.5.2 容错处理
系统的容错处理策略：

**数据冗余**：
- 配置参数多重备份
- 重要数据同时存储到Flash和SD卡

**降级运行**：
- SD卡故障时使用Flash存储
- 显示故障时通过串口输出

**自动恢复**：
- 检测到错误时自动重试
- 配置损坏时恢复默认值

### 3.6 状态管理

#### 3.6.1 系统状态定义
系统定义的主要状态：

```c
// 采样状态
typedef enum {
    SAMPLING_IDLE,     // 空闲
    SAMPLING_ACTIVE    // 采样中
} sampling_state_t;

// 存储状态
typedef enum {
    DATA_STORAGE_OK,       // 存储正常
    DATA_STORAGE_ERROR,    // 存储错误
    DATA_STORAGE_NO_SD     // 无SD卡
} storage_state_t;
```

#### 3.6.2 状态转换
状态转换通过标准接口函数实现：
```c
// 状态转换函数
sampling_result_t sampling_start(void);
sampling_result_t sampling_stop(void);
sampling_state_t sampling_get_state(void);
```

## 第四章 工程系统优化

### 4.1 可靠性优化

#### 4.1.1 时序控制优化
在不改变功能的前提下，提高系统时序的可靠性：

**任务执行时间控制**：
```c
// 任务执行时间监控
uint32_t task_start_time = HAL_GetTick();
led_task();  // 执行LED任务
uint32_t task_duration = HAL_GetTick() - task_start_time;
if (task_duration > MAX_TASK_TIME) {
    // 任务执行超时处理
    log_error("Task timeout");
}
```

**防抖算法优化**：
```c
// 按键防抖处理
static uint32_t last_key_time = 0;
if (HAL_GetTick() - last_key_time > DEBOUNCE_TIME) {
    // 处理按键事件
    last_key_time = HAL_GetTick();
}
```

**中断优先级设置**：
- 定时器中断：最高优先级（保证时序准确）
- 串口中断：高优先级（防止数据丢失）
- 按键中断：中等优先级（防止误触发）

#### 4.1.2 内存管理优化
优化内存使用，提高系统稳定性：

**栈溢出检测**：
```c
// 检查栈使用情况
extern uint32_t _estack;
uint32_t stack_used = (uint32_t)&_estack - (uint32_t)__get_MSP();
if (stack_used > STACK_WARNING_SIZE) {
    log_warning("Stack usage high");
}
```

**缓冲区保护**：
```c
// 安全的字符串复制
void safe_strcpy(char* dest, const char* src, size_t dest_size) {
    strncpy(dest, src, dest_size - 1);
    dest[dest_size - 1] = '\0';  // 确保字符串结束
}
```

#### 4.1.3 存储系统优化
提高数据存储的可靠性：

**数据校验机制**：
```c
// CRC32校验
uint32_t crc = HAL_CRC_Calculate(&hcrc, (uint32_t*)data, length);
config.crc32 = crc;  // 保存校验值
```

**错误恢复机制**：
```c
// SD卡错误恢复
if (f_write(&file, data, size, &written) != FR_OK) {
    f_close(&file);           // 关闭文件
    f_mount(NULL, "", 0);     // 卸载文件系统
    f_mount(&fs, "", 1);      // 重新挂载
    // 重试写入操作
}
```

### 4.2 一致性保证

#### 4.2.1 数据一致性
确保系统各部分数据的一致性：

**配置参数同步**：
```c
// 配置参数更新时同步到Flash
void config_update_cycle(sampling_cycle_t new_cycle) {
    g_config.cycle = new_cycle;        // 更新RAM中的配置
    config_save_to_flash();            // 同步到Flash
    sampling_set_cycle(new_cycle);     // 应用到采样模块
}
```

**状态信息同步**：
```c
// 状态变更时同步更新
void sampling_state_change(sampling_state_t new_state) {
    g_sampling_state = new_state;      // 更新全局状态
    oled_update_display();             // 更新显示
    led_update_status();               // 更新LED指示
    log_state_change(new_state);       // 记录状态变更
}
```

#### 4.2.2 时序一致性
保证系统时序的一致性：

**时间基准统一**：
```c
// 统一使用HAL_GetTick()作为时间基准
uint32_t current_time = HAL_GetTick();
```

**采样时间戳精确记录**：
```c
// 采样时记录精确时间戳
typedef struct {
    uint32_t timestamp;    // Unix时间戳
    float voltage;         // 电压值
} sample_data_t;
```

#### 4.2.3 接口一致性
保证各接口的一致性：

**统一的返回值**：
```c
// 所有函数使用统一的返回值类型
typedef enum {
    RESULT_OK,
    RESULT_ERROR,
    RESULT_INVALID_PARAM
} result_t;
```

**统一的错误处理**：
```c
// 统一的错误处理函数
void handle_error(const char* module, const char* error_msg) {
    data_storage_write_log(error_msg);  // 记录日志
    // 其他错误处理逻辑
}
```

### 4.3 性能优化

#### 4.3.1 执行效率优化
提高系统执行效率的方法：

**算法优化**：
```c
// 使用位操作提高效率
#define SET_BIT(reg, bit)    ((reg) |= (1 << (bit)))
#define CLEAR_BIT(reg, bit)  ((reg) &= ~(1 << (bit)))
#define TOGGLE_BIT(reg, bit) ((reg) ^= (1 << (bit)))
```

**批量操作**：
```c
// 批量写入数据到SD卡
void batch_write_samples(sample_data_t* samples, uint32_t count) {
    for (uint32_t i = 0; i < count; i++) {
        // 累积数据到缓冲区
    }
    // 一次性写入文件
    f_write(&file, buffer, buffer_size, &written);
}
```

#### 4.3.2 资源利用优化
合理利用系统资源：

**内存优化**：
- 使用静态内存分配，避免碎片
- 合理设置缓冲区大小
- 及时释放不用的资源

**存储优化**：
- Flash按扇区擦除，提高效率
- SD卡预分配文件空间
- 定期清理过期数据

### 4.4 稳定性优化

#### 4.4.1 抗干扰优化
提高系统抗干扰能力：

**软件滤波**：
```c
// 简单的滑动平均滤波
float moving_average_filter(float new_value) {
    static float buffer[FILTER_SIZE] = {0};
    static uint32_t index = 0;

    buffer[index] = new_value;
    index = (index + 1) % FILTER_SIZE;

    float sum = 0;
    for (int i = 0; i < FILTER_SIZE; i++) {
        sum += buffer[i];
    }
    return sum / FILTER_SIZE;
}
```

**看门狗保护**：
```c
// 定期喂狗，防止系统死锁
void watchdog_refresh(void) {
    HAL_IWDG_Refresh(&hiwdg);
}
```

#### 4.4.2 故障恢复优化
提高系统故障恢复能力：

**自动重试机制**：
```c
// 操作失败时自动重试
result_t retry_operation(operation_func_t func, uint32_t max_retries) {
    for (uint32_t i = 0; i < max_retries; i++) {
        if (func() == RESULT_OK) {
            return RESULT_OK;
        }
        HAL_Delay(100);  // 延时后重试
    }
    return RESULT_ERROR;
}
```

## 第五章 系统功能调试

### 5.1 调试环境准备

#### 5.1.1 硬件连接
调试前的硬件连接检查：

1. **电源连接**：确认3.3V供电正常
2. **串口连接**：UART接口，115200bps
3. **SD卡**：插入FAT32格式的SD卡
4. **OLED显示**：检查I2C连接
5. **按键**：确认KEY1-KEY4正常
6. **LED指示**：检查LED1-LED6状态

#### 5.1.2 软件工具
准备调试所需的软件工具：

1. **串口工具**：串口助手或终端软件
2. **文件管理**：SD卡文件浏览器
3. **时间工具**：RTC时间设置工具
4. **数据分析**：采样数据分析软件

#### 5.1.3 系统自检
使用代码中的自检功能：

```c
// 执行系统自检
void perform_sys_diagnostics(void) {
    printf("=== System Diagnostics ===\n");

    // 检查Flash
    if (check_storage_flash(&flash_info) == SYSTEM_CHECK_OK) {
        printf("Flash: OK, ID=0x%06X\n", flash_info.flash_id);
    } else {
        printf("Flash: ERROR\n");
    }

    // 检查SD卡
    if (check_storage_tf(&sd_info) == SYSTEM_CHECK_OK) {
        printf("SD Card: OK, Size=%luMB\n", sd_info.capacity_mb);
    } else {
        printf("SD Card: ERROR\n");
    }

    // 检查RTC
    printf("RTC Time: %s\n", get_current_time_string());
}
```

### 5.2 功能调试步骤

#### 5.2.1 数据采集调试
调试数据采集功能：

**基础采样测试**：
```c
// 测试ADC采样
uint32_t adc_value = HAL_ADC_GetValue(&hadc1);
float voltage = (float)adc_value * 3.3f / 4096.0f;
printf("ADC: %lu, Voltage: %.2fV\n", adc_value, voltage);
```

**周期采样测试**：
1. 发送`start`命令启动采样
2. 观察LED1闪烁（1秒周期）
3. 监控串口输出数据
4. 验证采样周期准确性

**按键控制测试**：
- KEY1：启动/停止采样
- KEY2：停止采样
- KEY3：设置10秒周期
- KEY4：设置15秒周期

#### 5.2.2 数据存储调试
调试数据存储功能：

**存储测试命令**：
```c
// 使用teststorage命令测试
void test_data_storage(void) {
    // 测试普通采样数据存储
    if (data_storage_write_sample(3.25f) == DATA_STORAGE_OK) {
        printf("Sample storage: OK\n");
    }

    // 测试超限数据存储
    if (data_storage_write_overlimit(3.45f, 3.30f) == DATA_STORAGE_OK) {
        printf("Overlimit storage: OK\n");
    }

    // 测试日志存储
    if (data_storage_write_log("Test log message") == DATA_STORAGE_OK) {
        printf("Log storage: OK\n");
    }
}
```

**文件检查**：
检查SD卡中生成的文件：
- `sample/` 目录：普通采样数据
- `overlimit/` 目录：超限数据
- `log/` 目录：系统日志
- `hideData/` 目录：隐藏数据

#### 5.2.3 人机交互调试
调试用户交互功能：

**OLED显示测试**：
- 空闲时显示："systemidle"
- 采样时显示：时间和电压值
- 格式：`HH:MM:SS XX.XX V`

**串口命令测试**：
主要命令的测试：
- `test` - 系统自检
- `start` - 开始采样
- `stop` - 停止采样
- `teststorage` - 测试存储
- `RTC Config` - 设置时间

### 5.3 集成调试

#### 5.3.1 模块协调测试
测试各模块间的协调工作：

**数据流测试**：
1. 启动采样，监控数据从ADC到存储的完整流程
2. 检查状态信息在各模块间的同步
3. 验证时序协调机制

**接口测试**：
测试硬件和软件接口的集成：
```c
// 测试模块间接口
void test_module_interfaces(void) {
    // 测试ADC接口
    float voltage = adc_read_voltage();

    // 测试存储接口
    data_storage_write_sample(voltage);

    // 测试显示接口
    oled_display_voltage(voltage);

    // 测试LED接口
    led_update_status();
}
```

#### 5.3.2 性能测试
测试系统的性能指标：

**响应时间测试**：
```c
// 测试按键响应时间
uint32_t start_time = HAL_GetTick();
// 按键处理
uint32_t response_time = HAL_GetTick() - start_time;
printf("Key response time: %lums\n", response_time);
```

**资源使用测试**：
- 监控内存使用情况
- 检查Flash和SD卡空间
- 测试长期运行稳定性

#### 5.3.3 故障测试
测试系统的故障处理能力：

**故障模拟**：
```c
// 模拟SD卡故障
void simulate_sd_card_failure(void) {
    // 强制卸载SD卡
    f_mount(NULL, "", 0);

    // 测试系统响应
    data_storage_write_sample(3.3f);

    // 检查是否切换到Flash存储
}
```

**恢复测试**：
- 测试自动重试机制
- 验证配置恢复功能
- 检查系统重启恢复

## 附录：产品使用手册

### A.1 产品概述

#### A.1.1 功能介绍
这是一个基于sysFunction程序的数据采集系统，主要功能包括：

- **数据采集**：12位ADC电压采集，测量范围0-3.3V
- **实时显示**：OLED屏幕显示时间和电压值
- **数据存储**：SD卡和Flash双重存储
- **状态指示**：LED显示系统状态和报警
- **用户操作**：4个按键控制系统
- **串口通信**：命令行控制和数据输出

#### A.1.2 技术参数
- 主控制器：GD32F470，200MHz
- 存储：3MB Flash + 512KB RAM + SD卡
- 采样精度：12位，±0.1%
- 采样周期：5s/10s/15s可配置
- 通信：UART 115200bps
- 电源：3.3V±5%

#### A.1.3 安装连接
**硬件连接**：
1. 连接3.3V电源
2. 插入FAT32格式SD卡
3. 连接OLED显示屏（I2C）
4. 连接串口线（115200bps）
5. 检查按键KEY1-KEY4

**首次启动**：
1. 上电后观察启动过程
2. 串口输出设备ID
3. 系统自动执行自检
4. OLED显示"systemidle"表示就绪

### A.2 基本操作

#### A.2.1 数据采集操作
**启动采样**：
- 按键：按下KEY1启动采样
- 串口：发送`start`命令
- 状态：OLED显示时间和电压，LED1闪烁

**停止采样**：
- 按键：按下KEY2停止采样
- 串口：发送`stop`命令
- 状态：OLED显示"systemidle"，LED1熄灭

**调整周期**：
- KEY3：设置10秒采样周期
- KEY4：设置15秒采样周期
- 默认：5秒周期

#### A.2.2 串口命令
**主要命令**：
```c
test        // 系统自检
start       // 开始采样
stop        // 停止采样
teststorage // 测试存储
RTC Config  // 设置时间
hidden      // 隐藏模式
normal      // 普通模式
```

#### A.2.3 数据文件
**存储结构**：
```
SD卡/
├── sample/     # 采样数据
├── overlimit/  # 超限数据
├── log/        # 系统日志
└── hideData/   # 隐藏数据
```

**文件格式**：
- 普通数据：`时间戳 电压值`
- 超限数据：`时间戳 电压值 限值`
- 系统日志：`时间戳 操作描述`
- 隐藏数据：十六进制编码

**文件命名**：
格式：`类型_日期_启动次数_索引.txt`
例如：`sample_20250101_001_001.txt`

### A.3 故障处理

#### A.3.1 常见故障
**系统无响应**：
- 检查3.3V电源供电
- 按复位按钮重启
- 检查串口连接（115200bps）

**启动异常**：
- 重新插拔SD卡
- 确认SD卡为FAT32格式
- 使用`test`命令自检

**采样异常**：
- 确认输入电压在0-3.3V范围
- 检查ADC信号线连接
- 验证参考电压稳定性

**存储失败**：
- 使用`teststorage`测试存储
- 重新格式化SD卡
- 检查SD卡剩余空间

**通信异常**：
- 检查串口线连接
- 确认波特率115200bps
- 重新连接I2C显示屏

#### A.3.2 日常维护
**存储管理**：
- 定期检查SD卡空间
- 清理过期数据文件
- 备份重要数据

**系统监控**：
- 定期执行`test`命令
- 观察系统运行日志
- 监控长期稳定性

**硬件保养**：
- 保持设备清洁
- 检查连接线缆
- 避免高温高湿环境

### A.4 技术规格

#### A.4.1 硬件规格
**主控制器**：
- 型号：GD32F470 ARM Cortex-M4
- 主频：200MHz
- Flash：3MB
- RAM：512KB
- 电压：3.3V±5%

**数据采集**：
- ADC：12位分辨率
- 测量范围：0-3.3V
- 精度：±0.1%
- 采样周期：5s/10s/15s

**存储系统**：
- 内部Flash：3MB
- SD卡：最大32GB（FAT32）
- 文件系统：FATFS
- 数据校验：CRC32

**显示通信**：
- OLED：128x64像素，I2C接口
- 串口：115200bps，8N1格式
- 调试：SWD接口

#### A.4.2 性能指标
**响应时间**：
- 按键响应：<10ms
- 串口响应：<50ms
- 显示更新：<5ms
- 数据存储：<100ms

**可靠性**：
- 连续运行：>30天
- 数据完整性：99.99%
- 启动时间：<3秒
- 故障恢复：<5秒

**功耗**：
- 工作电流：<80mA@3.3V
- 待机电流：<5mA@3.3V
- 平均功耗：<200mW

#### A.4.3 环境要求
- 工作温度：-40°C ~ +85°C
- 相对湿度：5% ~ 95%（无凝露）
- 电源纹波：<50mV
- 抗振动：10-55Hz

---

**版权信息**：
- 版本：v1.0
- 编制：中北大学
- 日期：2024年12月
- 基于：sysFunction程序设计

*本文档基于sysFunction代码分析编写，版权所有 © 2024 中北大学*
